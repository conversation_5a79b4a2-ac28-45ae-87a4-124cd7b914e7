package com.doudian.open.api.material_batchUploadVideoAsync;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.material_batchUploadVideoAsync.data.*;

//auto generated, do not edit

public class MaterialBatchUploadVideoAsyncResponse extends DoudianOpResponse<MaterialBatchUploadVideoAsyncData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}