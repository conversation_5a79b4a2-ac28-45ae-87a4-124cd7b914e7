package com.wendao101.common.core.kuaishou.pay;

import com.google.common.base.Joiner;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class SettleDTO {
    private String out_order_no; // 开发者需要发起结算的支付订单号，商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一
    private String out_settle_no; // 开发者的结算单号，小程序唯一
    private String reason; // 结算描述，长度限制 128 个字符。1个字符=2个汉字
    private String attach; // 开发者自定义字段，回调原样回传. 注：1汉字=2字符；勿回传敏感信息
    private String notify_url; // 通知URL必须为直接可访问的URL，不允许携带查询串
    private String sign; // 开发者对核心字段签名, 防止传输过程中出现意外，签名方式见附录
    private Integer settle_amount; // 当次结算金额，需传大于0的金额，单位为【分】；不传默认全额结算
    //private String multi_copies_goods_info="[{\"copies\":1}]"; // 单商品购买多份场景，示例值：[{"copies":2}]， 内容见multi_copies_goods_info字段说明


    public String createSign(String appId, String appSecret) {

        //{"attach":"ks_pay","detail":"测试商品","expire_time":600,"goods_detail_url":"/page/index/anima","multi_copies_goods_info":"[{\"copies\":1}]","notify_url":"https://www.wendao101.com/ksPayNotify",
        // "open_id":"f18d6db7dd8bf64c14bf57244173ea64","out_order_no":"123456789","sign":"5cfae0117f658b5758cbda1c100116a4","subject":"测试商品","total_amount":1000,"type":1298}
        Map<String, Object> signParamsMap = new HashMap<>();
        signParamsMap.put("app_id", appId);
        signParamsMap.put("out_order_no", this.getOut_order_no());
        signParamsMap.put("out_settle_no", this.getOut_settle_no());
        signParamsMap.put("reason", this.getReason());
        //signParamsMap.put("attach", this.getTotal_amount());
        signParamsMap.put("notify_url", this.getNotify_url());
        //signParamsMap.put("refund_amount", this.getRefund_amount());
        // 去掉 value 为空的
        Map<String, Object> trimmedParamMap = signParamsMap.entrySet()
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getValue().toString()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 按照字母排序
        Map<String, Object> sortedParamMap = trimmedParamMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        // 组装成待签名字符串。(注，引用了guava工具)
        String paramStr = Joiner.on("&").withKeyValueSeparator("=").join(sortedParamMap.entrySet());
        String signStr = paramStr + appSecret;

        // 生成签名返回。(注，引用了commons-codec工具)
        return DigestUtils.md5Hex(signStr);
    }
}
