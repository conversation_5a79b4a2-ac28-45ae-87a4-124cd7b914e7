package com.wendao101.common.core.douyin.login;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wendao101.common.core.douyin.login.child.Code2SessionResultData;
import lombok.Data;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Code2SessionResult {

    // 错误码
    @JsonProperty("err_no")
    private Long errNo;

    // 错误信息
    @JsonProperty("err_tips")
    private String errTips;

    @JsonProperty("data")
    private Code2SessionResultData data;

}

