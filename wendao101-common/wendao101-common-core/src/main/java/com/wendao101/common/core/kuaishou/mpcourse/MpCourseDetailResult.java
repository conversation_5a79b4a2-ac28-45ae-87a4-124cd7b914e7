package com.wendao101.common.core.kuaishou.mpcourse;

import com.wendao101.common.core.kuaishou.mpcourse.child.MpCourseDetailResultData;
import com.wendao101.common.core.kuaishou.mpcourse.child.MpCourseVersionResultData;
import lombok.Data;

import java.util.List;

/**
 {
 "result": 1,
 "error_msg": "success",
 "data": {
 "mpCourseAlbumInfo": {
 "title": "测试标题",
 "totalEpisodeNumber": 10,
 "cover": "08e4fdaa0110c190e9c702",
 "path": "page/component/pages/checkboxV1/checkbox?ksCourseId=kmc5199405898388293464",
 "introduce": "这是一个介绍",
 "chargeOrNot": true,
 "updateDoneOrNot": false,
 "notifyUrl": "https://data.mofangout.com/message/send",
 "secondCategory": 30,
 "purchasePrecation": "购买告警",
 "teachType": 1,
 "serviceType": 3,
 "validPeriod": 1,
 "supportTrial": true,
 "teacherInfo": {
 "name": "老师名字",
 "introduce": "老师介绍",
 "imgKey": "08b180ab0110a2ecf9ce07"
 },
 "giftInfo": {
 "name": "赠品名称",
 "amount": 10,
 "refundPolicy": "退款策略",
 "type": 2
 },
 "auditStatus": 2,
 "failReason":"封面课程不相关"
 },
 "episodeInfoList": [
 {
 "title": "第一集标题",
 "episodeNumber": 1,
 "videoId": "48736c7442b4417c",
 "audioId": "84b511784a0f426f",
 "imageIdList": [
 "08e4fdaa0110c190e9c702"
 ],
 "introduce": "第一集介绍1",
 "supportTrial": false,
 "path": "page/component/pages/checkboxV1/checkbox?ksCourseId=kmc5199405898388293464&ksSeq=1",
 "failReason": "",
 "auditStatus": 2
 },
 {
 "title": "第二集标题",
 "episodeNumber": 2,
 "videoId": "48736c7442b4417c",
 "audioId": "84b511784a0f426f",
 "imageIdList": [
 "08e4fdaa0110c190e9c702"
 ],
 "introduce": "第二集介绍",
 "supportTrial": false,
 "path": "page/component/pages/checkboxV1/checkbox?ksCourseId=kmc5199405898388293464&ksSeq=2",
 "failReason": "",
 "auditStatus": 2
 },
 {
 "title": "第3集标题",
 "episodeNumber": 3,
 "videoId": "48736c7442b4417c",
 "audioId": "84b511784a0f426f",
 "imageIdList": [
 "08e4fdaa0110c190e9c702"
 ],
 "introduce": "第3集介绍",
 "supportTrial": false,
 "path": "page/component/pages/checkboxV1/checkbox?ksCourseId=kmc5199405898388293464&ksSeq=3",
 "failReason": "",
 "auditStatus": 2
 },
 {
 "title": "第4集标题",
 "episodeNumber": 4,
 "videoId": "48736c7442b4417c",
 "audioId": "84b511784a0f426f",
 "imageIdList": [
 "08e4fdaa0110c190e9c702"
 ],
 "introduce": "第4集介绍",
 "supportTrial": false,
 "path": "page/component/pages/checkboxV1/checkbox?ksCourseId=kmc5199405898388293464&ksSeq=4",
 "failReason": "",
 "auditStatus": 2
 }
 ],
 "versionInfo": {
 "version": 3,
 "auditStatus": 2,
 "auditStatusDesc": "审核通过",
 "onlineStatus": 2,
 "onlineStatusDesc": "未上线"
 }
 }
 }
 */
@Data
public class MpCourseDetailResult {
    private Integer result;
    private String error_msg;
    private MpCourseDetailResultData data;
}
