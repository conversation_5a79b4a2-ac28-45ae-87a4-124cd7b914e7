package com.wendao101.common.core.utils.poi;


import com.wendao101.common.core.annotation.ExcelField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 导出Excel
 */
@Slf4j
public class ExcelFieldUtil {
    /**
     * 读取excel文件内容到List中
     *
     * @param clazz 数据实体类的class
     * @param excel excel文件
     * @param <T>   数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, String excel) {
        return read(clazz, excel, null, 0);
    }

    /**
     * 读取excel文件内容到List中
     *
     * @param clazz 数据实体类的class
     * @param excel excel文件
     * @param <T>   数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, File excel) {
        return read(clazz, excel, null, 0);
    }

    /**
     * 读取excel文件内容到List中
     *
     * @param clazz       数据实体类的class
     * @param inputStream excel文件
     * @param <T>         数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, InputStream inputStream) {
        return read(clazz, inputStream, null, 0);
    }

    /**
     * 读取excel文件内容到List中
     *
     * @param clazz     数据实体类的class
     * @param excel     excel文件
     * @param sheetName 工作表名（默认取第一个工作表）
     * @param <T>       数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, String excel, String sheetName, Integer titleRowIndex) {
        Assert.hasText(excel, "excel文件不能为空");
        return read(clazz, new File(excel), sheetName, titleRowIndex);
    }

    /**
     * 读取excel文件内容到List中
     *
     * @param clazz     数据实体类的class
     * @param excel     excel文件
     * @param sheetName 工作表名（默认取第一个工作表）
     * @param <T>       数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, File excel, String sheetName, Integer titleRowIndex) {
        Assert.notNull(excel, "excel文件不能为空");
        try {
            InputStream inputStream = new FileInputStream(excel);
            return read(clazz, inputStream, sheetName, titleRowIndex);
        } catch (FileNotFoundException exception) {
            log.error("excel file not exists");
            throw new IllegalArgumentException("excel文件不存在");
        }
    }

    /**
     * 读取excel文件内容到List中
     *
     * @param clazz         数据实体类的class
     * @param inputStream   excel文件
     * @param sheetName     工作表名（默认取第1个工作表）
     * @param titleRowIndex 标题所在行索引（起始地址为0，默认取第1行）
     * @param <T>           数据实体类
     * @return 包含指定工作表内容的数据的list
     */
    public static <T> List<T> read(Class<? extends T> clazz, InputStream inputStream, String sheetName, Integer titleRowIndex) {
        Assert.notNull(clazz, "实体类不能为空");
        Assert.notNull(inputStream, "excel文件不能为空");
        if (titleRowIndex == null || titleRowIndex < 0) {
            titleRowIndex = 0;
        }
        try (XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            XSSFSheet sheet;
            if (StringUtils.hasText(sheetName)) {
                sheet = workbook.getSheet(sheetName);
            } else {
                sheet = workbook.getSheetAt(0);
            }
            Assert.notNull(sheet, "指定或默认的sheet不存在");
            Map<Integer, Field> columnIndexFieldMap = readTitle(clazz, sheet, titleRowIndex);
            Assert.notEmpty(columnIndexFieldMap, "在excel中未找到有效数据");
            return readData(clazz, sheet, titleRowIndex, columnIndexFieldMap);
        } catch (IOException | InstantiationException | IllegalAccessException exception) {
            log.error("throw Exception when read excel:{}", exception.getMessage());
            throw new IllegalArgumentException(exception.getMessage());
        }
    }

    /**
     * 读取excel数据
     *
     * @param clazz               数据实体类的class
     * @param sheet               工作表对象
     * @param titleRowIndex       标题所在行索引
     * @param columnIndexFieldMap 列索引与数据实体类字段Field的映射map
     * @param <T>                 数据实体类
     * @return excel数据
     * @throws IllegalAccessException 没有设置属性的权限
     * @throws InstantiationException 实例化对象失败
     */
    private static <T> List<T> readData(Class<? extends T> clazz, XSSFSheet sheet, Integer titleRowIndex, Map<Integer, Field> columnIndexFieldMap) throws IllegalAccessException, InstantiationException {
        List<T> list = new ArrayList<>();
        for (int rowIndex = titleRowIndex + 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            XSSFRow row = sheet.getRow(rowIndex);
            T data = clazz.newInstance();
            for (Map.Entry<Integer, Field> entry : columnIndexFieldMap.entrySet()) {
                Integer colIndex = entry.getKey();
                Field field = entry.getValue();
                Object value = getCell(row, colIndex, field.getType());
                field.setAccessible(true);
                field.set(data, value);
            }
            list.add(data);
        }
        return list;
    }

    /**
     * 读取excel标题，获取列索引与数据实体类字段Field的映射map
     *
     * @param clazz         数据实体类的class
     * @param sheet         工作表对象
     * @param titleRowIndex 标题所在行索引
     * @return 列索引与数据实体类字段Field的映射map
     */
    private static Map<Integer, Field> readTitle(Class<?> clazz, XSSFSheet sheet, Integer titleRowIndex) {
        List<FieldProperties> properties = getFieldProperties(clazz);
        Map<String, Field> columnNameFieldMap = new HashMap<>();
        for (FieldProperties property : properties) {
            String columnName = property.getExcelField().columnName();
            columnNameFieldMap.put(columnName, property.getField());
        }
        Map<Integer, Field> columnIndexFieldMap = new HashMap<>();
        XSSFRow row = sheet.getRow(titleRowIndex);
        Assert.notNull(row, "指定或默认的标题行不存在");
        for (int columnIndex = 0; columnIndex < row.getLastCellNum(); columnIndex++) {
            String titleName = getCell(row, columnIndex, String.class);
            Field field = columnNameFieldMap.get(titleName);
            if (field != null) {
                columnIndexFieldMap.put(columnIndex, field);
            }
        }
        return columnIndexFieldMap;
    }

    /**
     * 获取单元格内容，单元格内容转为对应字段的类型
     *
     * @param row      行
     * @param colIndex 列索引
     * @param clazz    对应字段类型的class
     * @param <T>      对应字段类型
     * @return 已转为对应字段类型的单元格内容
     */
    private static <T> T getCell(XSSFRow row, int colIndex, Class<? extends T> clazz) {
        XSSFCell cell = row.getCell(colIndex);
        if (cell == null) {
            cell = row.createCell(colIndex);
        }
        String name = clazz.getName();
        Object cellValue;
        switch (name) {
            case "byte":
            case "java.lang.Byte":
                cellValue = (byte) cell.getNumericCellValue();
                break;
            case "short":
            case "java.lang.Short":
                cellValue = (short) cell.getNumericCellValue();
                break;
            case "int":
            case "java.lang.Integer":
                cellValue = (int) cell.getNumericCellValue();
                break;
            case "long":
            case "java.lang.Long":
                cellValue = (long) cell.getNumericCellValue();
                break;
            case "float":
            case "java.lang.Float":
                cellValue = (float) cell.getNumericCellValue();
                break;
            case "double":
            case "java.lang.Double":
                cellValue = cell.getNumericCellValue();
                break;
            case "char":
            case "java.lang.Character":
                cellValue = (char) cell.getNumericCellValue();
                break;
            case "boolean":
            case "java.lang.Boolean":
                cellValue = cell.getBooleanCellValue();
                break;
            case "java.util.Date":
                cellValue = cell.getDateCellValue();
                break;
            case "java.time.LocalDate":
                cellValue = cell.getLocalDateTimeCellValue() == null ? null : cell.getLocalDateTimeCellValue().toLocalDate();
                break;
            case "java.time.LocalTime":
                cellValue = cell.getLocalDateTimeCellValue() == null ? null : cell.getLocalDateTimeCellValue().toLocalTime();
                break;
            case "java.time.LocalDateTime":
                cellValue = cell.getLocalDateTimeCellValue();
                break;
            case "java.lang.String":
                cellValue = cell.toString().trim();
                break;
            case "java.math.BigInteger":
                String strValue = cell.toString().trim();
                int dotIndex = strValue.indexOf('.');
                if (dotIndex >= 0) {
                    strValue = strValue.substring(0, dotIndex);
                }
                cellValue = StringUtils.hasText(strValue) ? new BigInteger(strValue) : null;
                break;
            case "java.math.BigDecimal":
                cellValue = StringUtils.hasText(cell.toString().trim()) ? new BigDecimal(cell.toString().trim()) : null;
                break;
            default:
                cellValue = cell.toString().trim();
        }
        return (T) cellValue;
    }

    /**
     * 导出excel文件
     *
     * @param collection 数据集合
     * @param <T>        数据实体类
     * @return workbook对象
     */
    public static <T> XSSFWorkbook write(Collection<T> collection) {
        return write(collection, null, null);
    }

    /**
     * 导出excel文件
     *
     * @param collection 数据集合
     * @param clazz      数据实体类的class
     * @param <T>        数据实体类
     * @return workbook对象
     */
    public static <T> XSSFWorkbook write(Collection<T> collection, Class<?> clazz) {
        return write(collection, clazz, null);
    }

    /**
     * 导出excel文件
     *
     * @param collection 数据集合
     * @param sheetName  工作表表名
     * @param <T>        数据实体类
     * @return workbook对象
     */
    public static <T> XSSFWorkbook write(Collection<T> collection, String sheetName) {
        return write(collection, null, sheetName);
    }

    /**
     * 导出excel文件
     *
     * @param collection 数据集合
     * @param clazz      数据实体类的class
     * @param sheetName  工作表表名
     * @param <T>        数据实体类
     * @return workbook对象
     */
    public static <T> XSSFWorkbook write(Collection<T> collection, Class<?> clazz, String sheetName) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet(StringUtils.hasText(sheetName) ? sheetName : "Sheet0");
        XSSFCellStyle titleStyle = createCellStyle(workbook);
        List<FieldProperties> properties = null;
        if (clazz != null) {
            properties = getFieldProperties(clazz);
            writeTitle(sheet, properties, titleStyle);
        }
        if (collection == null || collection.isEmpty()) {
            return workbook;
        }
        if (properties == null) {
            Optional<T> any = collection.stream().filter(o -> o != null).findAny();
            if (!any.isPresent()) {
                return workbook;
            }
            properties = getFieldProperties(any.get().getClass());
            writeTitle(sheet, properties, titleStyle);
        }
        try {
            writeData(sheet, properties, collection);
        } catch (IllegalAccessException exception) {
            log.error("throw IllegalAccessException when writeData:{}", exception.getMessage());
        }
        return workbook;
    }

    /**
     * 返回表头的单元格格式
     *
     * @param workbook workbook对象
     * @return 绿色背景的单元格格式
     */
    private static XSSFCellStyle createCellStyle(XSSFWorkbook workbook) {
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return titleStyle;
    }

    /**
     * 写入数据
     *
     * @param sheet      工作表
     * @param properties 表头
     * @param collection 数据
     * @param <T>        数据实体类
     * @throws IllegalAccessException 访问数据字段值时无访问权限
     */
    private static <T> void writeData(XSSFSheet sheet, List<FieldProperties> properties, Collection<T> collection) throws IllegalAccessException {
        int rowIndex = 1;
        XSSFRow row;
        for (T element : collection) {
            if (element == null) {
                continue;
            }
            row = sheet.createRow(rowIndex++);
            for (int index = 0; index < properties.size(); index++) {
                FieldProperties property = properties.get(index);
                Object value = property.getField().get(element);
                if (value == null) {
                    continue;
                }
                String type = property.getField().getType().getSimpleName();
                switch (type) {
                    case "Date":
                        SimpleDateFormat format = new SimpleDateFormat(property.getExcelField().format());
                        row.createCell(index).setCellValue(format.format(value));
                        break;
                    case "LocalDate":
                    case "LocalTime":
                    case "LocalDateTime":
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(property.getExcelField().format());
                        row.createCell(index).setCellValue(formatter.format((TemporalAccessor) value));
                        break;
                    default:
                        row.createCell(index).setCellValue(value.toString());
                }
            }
        }
    }

    /**
     * 写入表头
     *
     * @param sheet      工作表
     * @param properties 表头
     * @param titleStyle 表头格式
     */
    private static void writeTitle(XSSFSheet sheet, List<FieldProperties> properties, XSSFCellStyle titleStyle) {
        XSSFRow row = sheet.createRow(0);
        for (int index = 0; index < properties.size(); index++) {
            ExcelField excelField = properties.get(index).getExcelField();
            String columnName = excelField.columnName();
            XSSFCell cell = row.createCell(index);
            cell.setCellValue(columnName);
            cell.setCellStyle(titleStyle);
            // 设置列宽
            int columnWidth = excelField.columnWidth();
            columnWidth = columnWidth < 0 || columnWidth > 255 ? 16 : columnWidth;
            sheet.setColumnWidth(index, columnWidth * 256);
        }
    }

    /**
     * 获取表头字段
     *
     * @param clazz 数据实体类的class
     * @return 排好序的表头字段
     */
    private static List<FieldProperties> getFieldProperties(Class<?> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        List<FieldProperties> properties = new ArrayList<>();
        for (Field declaredField : declaredFields) {
            ExcelField excelField = declaredField.getAnnotation(ExcelField.class);
            if (excelField == null) {
                continue;
            }
            declaredField.setAccessible(true);
            FieldProperties property = FieldProperties.builder().excelField(excelField).field(declaredField).build();
            properties.add(property);
        }
        properties.sort((o1, o2) -> o1.getExcelField().ordinal() - o2.getExcelField().ordinal());
        return properties;
    }

    @Getter
    @Setter
    @Builder
    private static class FieldProperties {
        private ExcelField excelField;
        private Field field;
    }
}
