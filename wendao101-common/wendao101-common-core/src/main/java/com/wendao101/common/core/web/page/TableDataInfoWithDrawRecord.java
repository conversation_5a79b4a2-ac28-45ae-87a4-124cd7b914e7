package com.wendao101.common.core.web.page;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class TableDataInfoWithDrawRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据 */
    private List<?> rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;

    private BigDecimal totalWithdrawnAmount = new BigDecimal(0);
    private BigDecimal totalServiceFee= new BigDecimal(0);
    private BigDecimal totalReceiveMoney= new BigDecimal(0);

    public BigDecimal getTotalWithdrawnAmount() {
        return totalWithdrawnAmount;
    }

    public void setTotalWithdrawnAmount(BigDecimal totalWithdrawnAmount) {
        this.totalWithdrawnAmount = totalWithdrawnAmount;
    }

    public BigDecimal getTotalServiceFee() {
        return totalServiceFee;
    }

    public void setTotalServiceFee(BigDecimal totalServiceFee) {
        this.totalServiceFee = totalServiceFee;
    }

    public BigDecimal getTotalReceiveMoney() {
        return totalReceiveMoney;
    }

    public void setTotalReceiveMoney(BigDecimal totalReceiveMoney) {
        this.totalReceiveMoney = totalReceiveMoney;
    }

    /**
     * 表格数据对象
     */
    public TableDataInfoWithDrawRecord()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfoWithDrawRecord(List<?> list, int total)
    {
        this.rows = list;
        this.total = total;
    }

    public static TableDataInfoWithDrawRecord error(String msg){
        TableDataInfoWithDrawRecord tableDataInfo = new TableDataInfoWithDrawRecord();
        tableDataInfo.setCode(500);
        tableDataInfo.setMsg(msg);
        return tableDataInfo;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public List<?> getRows()
    {
        return rows;
    }

    public void setRows(List<?> rows)
    {
        this.rows = rows;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }
}
