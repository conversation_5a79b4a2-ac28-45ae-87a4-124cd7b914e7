package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * {
 *   "GroupId": "@TGS#aC5SZEAEF",
 *   "GroupAttr":[
 *       {
 *           "key":"attr_key", //属性key
 *           "value":"attr_val" //属性value
 *       }，
 *       {
 *           "key":"attr_key1",
 *           "value":"attr_val1"
 *       }
 *   ]
 * }
 */
@Data
public class SetGroupAttrDTO {
    /**
     * 操作的群 ID
     */
    @JsonProperty("GroupId")
    @JSONField(name = "GroupId")
    String groupId;
    /**
     * 1: 设置管理员
     * 2: 取消设置管理员
     */
    @JsonProperty("GroupAttr")
    @JSONField(name = "GroupAttr")
    List<GroupAttrDTO> groupAttr;

}
