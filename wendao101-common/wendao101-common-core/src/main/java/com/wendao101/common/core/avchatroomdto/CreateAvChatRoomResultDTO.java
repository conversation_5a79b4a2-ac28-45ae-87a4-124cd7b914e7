package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *   "ActionStatus": "OK",
 *   "ErrorInfo": "",
 *   "ErrorCode": 0,
 *   "GroupId": "@TGS#_@TGS#cRDH3HIM62CP",
 *   "HugeGroupFlag": 0,
 *   "Type": "Community"
 * }
 */
@Data
public class CreateAvChatRoomResultDTO {
    /**
     * 请求处理的结果：
     * OK 表示处理成功
     * FAIL 表示失败
     */
    @JsonProperty("ActionStatus")
    @JSONField(name="ActionStatus")
    private String actionStatus;

    /**
     * 错误信息
     */
    @JsonProperty("ErrorInfo")
    @JSONField(name="ErrorInfo")
    private String errorInfo;

    /**
     * 错误码：
     * 0表示成功
     * 非0表示失败
     */
    @JsonProperty("ErrorCode")
    @JSONField(name="ErrorCode")
    private Integer errorCode;

    /**
     * 创建成功之后的群 ID，由即时通信 IM 后台分配
     */
    @JsonProperty("GroupId")
    @JSONField(name="GroupId")
    private String groupId;

    @JsonProperty("HugeGroupFlag")
    @JSONField(name="HugeGroupFlag")
    private Integer hugeGroupFlag;

    @JsonProperty("Type")
    @JSONField(name="Type")
    private String type;
}
