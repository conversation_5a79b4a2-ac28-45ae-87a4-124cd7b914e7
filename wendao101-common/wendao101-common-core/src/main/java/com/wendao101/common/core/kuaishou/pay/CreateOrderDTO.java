package com.wendao101.common.core.kuaishou.pay;

import com.google.common.base.Joiner;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class CreateOrderDTO {
    /**
     * 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一
     * 示例值：1217752501201407033233368018
     */
    private String out_order_no;
    /**
     * 快手用户在当前小程序的open_id，可通过login操作获取。
     */
    private String open_id;
    /**
     * 用户支付金额，单位为[分]。不允许传非整数的数值。
     */
    private Integer total_amount;
    /**
     * 商品名称或者商品描述简介，建议长度在10个汉字以内，在收银台页面、支付账单中供用户查看使用。注：不可传入特殊符号、emoji 表情等，否则会影响支付成功，1汉字=2字符。
     */
    private String subject;
    /**
     * 商品详情。注：不可传入特殊符号、emoji 表情等，否则会影响支付成功，1汉字=2字符。
     */
    private String detail;
    /**
     * 商品类型，不同商品类目的编号见 担保支付商品类目编号
     */
    private Integer type;
    /**
     * 订单过期时间，单位秒，300s - 172800s
     */
    private Integer expire_time;
    /**
     * 开发者对核心字段签名, 签名方式见 附录
     */
    private String sign;
    /**
     * 开发者自定义字段，回调原样回传.
     * 注：1汉字=2字符；勿回传敏感信息
     */
    private String attach;
    /**
     * 通知URL必须为直接可访问的URL，不允许携带查询串。
     */
    private String notify_url;
    /**
     * 下单商品id，需与商品对接 (opens new window)时的product_id一致，长度限制256个英文字符，1个汉字=2个英文字符；
     */
    private String goods_id;
    /**
     * 订单详情页跳转path。长度限制500个英文字符，1个汉字=2个英文字符；
     * 示例值：/page/index/anima
     */
    private String goods_detail_url;
    /**
     * 单商品购买多份场景，示例值：[{"copies":2}]， 内容见multi_copies_goods_info字段说明
     */
//    private String multi_copies_goods_info = "[{\"copies\":1}]";
    private String multi_copies_goods_info;
    /**
     * 该字段表示创建订单的同时是否覆盖之前已存在的订单。
     * 取值范围: [0, 1]。
     * 0:不覆盖
     * 1:覆盖
     * 使用说明：如果传值为1 重复调用接口后执行逻辑为先删除已存在的订单再创建新订单，如果传值为0 重复调用接口执行逻辑为直接返回已创建订单的订单信息。如果不传该参数则和传值为0逻辑一致
     */
    private Integer cancel_order;


    public String createSign(String appid, String appSecret,boolean isContainGoodsId) {
        //{"attach":"ks_pay","detail":"测试商品","expire_time":600,"goods_detail_url":"/page/index/anima","multi_copies_goods_info":"[{\"copies\":1}]","notify_url":"https://www.wendao101.com/ksPayNotify",
        // "open_id":"f18d6db7dd8bf64c14bf57244173ea64","out_order_no":"123456789","sign":"5cfae0117f658b5758cbda1c100116a4","subject":"测试商品","total_amount":1000,"type":1298}
        Map<String, Object> signParamsMap = new HashMap<>();
        signParamsMap.put("app_id", appid);
        signParamsMap.put("open_id", this.getOpen_id());
        signParamsMap.put("out_order_no", this.getOut_order_no());
        signParamsMap.put("total_amount", this.getTotal_amount());
        signParamsMap.put("subject", this.getSubject());
        signParamsMap.put("type", this.getType());
        signParamsMap.put("detail", this.getDetail());
        signParamsMap.put("expire_time", this.getExpire_time());
        signParamsMap.put("notify_url", this.getNotify_url());
        if(isContainGoodsId){
            signParamsMap.put("goods_id", this.getGoods_id());
        }
        // 去掉 value 为空的
        Map<String, Object> trimmedParamMap = signParamsMap.entrySet()
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getValue().toString()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 按照字母排序
        Map<String, Object> sortedParamMap = trimmedParamMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        // 组装成待签名字符串。(注，引用了guava工具)
        String paramStr = Joiner.on("&").withKeyValueSeparator("=").join(sortedParamMap.entrySet());
        String signStr = paramStr + appSecret;

        // 生成签名返回。(注，引用了commons-codec工具)
        return DigestUtils.md5Hex(signStr);
    }
}
