package com.wendao101.common.core.kuaishou.mpcourse.child;

import lombok.Data;

import java.util.List;

@Data
public class EpisodeInfoDetail {
    private String title;
    private int episodeNumber;
    private String videoId;
    private String audioId;
    private List<String> imageIdList;
    private String introduce;
    private boolean supportTrial;
    private String path;
    //failReason
    private String failReason;
    //auditStatus
    private int auditStatus;
}