package com.wendao101.common.core.kuaishou.pay.callback.child;

import lombok.Data;

@Data
public class CallBackData {
    /**
     * 支付渠道。取值：UNKNOWN - 未知｜WECHAT-微信｜ALIPAY-支付宝
     */
    private String channel;
    /**
     * 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一
     * 示例值：1217752501201407033233368018
     */
    private String out_order_no;
    /**
     * 预下单时携带的开发者自定义信息
     */
    private String attach;
    /**
     * 订单支付状态。 取值： PROCESSING-处理中｜SUCCESS-成功｜FAILED-失败
     */
    private String status;
    /**
     * 	快手小程序平台订单号
     */
    private String ks_order_no;
    /**
     * 订单金额
     */
    private int order_amount;
    /**
     * 用户侧支付页交易单号，具体获取方法可点击查看
     */
    private String trade_no;
    /**
     * 订单来源信息，同支付查询接口
     */
    private String extra_info;
    /**
     * 是否参与分销，true:分销，false:非分销
     */
    private boolean enable_promotion;
    /**
     * 	预计分销金额，单位：分
     */
    private int promotion_amount;
}
