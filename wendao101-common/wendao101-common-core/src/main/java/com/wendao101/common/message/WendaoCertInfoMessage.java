package com.wendao101.common.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;

import java.util.Date;

public class WendaoCertInfoMessage {

    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 实体类型 1: 个人 2:企业 */
    @Excel(name = "实体类型 1: 个人 2:企业")
    private Long entityType;

    /** 个人-身份证正面链接,人像面 */
    @Excel(name = "个人-身份证正面链接,人像面")
    private String frontPath;

    /** 个人-身份反面链接,国徽面 */
    @Excel(name = "个人-身份反面链接,国徽面")
    private String backPath;

    /** 个人-身份证号码 */
    @Excel(name = "个人-身份证号码")
    private String idNumber;

    /** 个人-老师真实姓名 */
    @Excel(name = "个人-老师真实姓名")
    private String teacherRealName;

    /** 个人-身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "个人-身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String idCardExpireTime;

    /** 个人和机构-头像 */
    @Excel(name = "个人和机构-头像")
    private String avatarPath;

    /** 个人和机构-昵称 */
    @Excel(name = "个人和机构-昵称")
    private String nickName;

    /** 个人和机构-老师介绍、200字以内 */
    @Excel(name = "个人和机构-老师介绍、200字以内")
    private String teacherDesc;

    /** 机构-营业执照编号或者统一社会信用代码 */
    @Excel(name = "机构-营业执照编号或者统一社会信用代码")
    private String businessLicenseNo;

    /** 机构-营业执照过期时间，示例：2029-09-10 */
    @Excel(name = "机构-营业执照过期时间，示例：2029-09-10")
    private String businessLicenseExpireTime;

    /** 机构-营业执照上的公司名称 */
    @Excel(name = "机构-营业执照上的公司名称")
    private String businessLicenseCompanyName;

    /** 机构-营业执照文件访问路径 */
    @Excel(name = "机构-营业执照文件访问路径")
    private String businessLicensePath;

    /** 机构-法人代表身份证正面链接,人像面 */
    @Excel(name = "机构-法人代表身份证正面链接,人像面")
    private String legalPersonFrontPath;

    /** 机构-法人代表身份反面链接,国徽面 */
    @Excel(name = "机构-法人代表身份反面链接,国徽面")
    private String legalPersonBackPath;

    /** 机构-法人代表姓名 */
    @Excel(name = "机构-法人代表姓名")
    private String legalPersonName;

    /** 机构-法人代表身份证号码 */
    @Excel(name = "机构-法人代表身份证号码")
    private String legalPersonIdNumber;

    /** 机构-法人代表身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构-法人代表身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String legalPersonIdCardExpireTime;

    /** 机构-机构场景类型（仅可填写"线上机构"或"线下机构"） */
    @Excel(name = "机构-机构场景类型")
    private String institutionSceneType;

    /** 机构-机构主体类型（仅可填"企业工商户"或"个体工商户"） */
    @Excel(name = "机构-机构主体类型")
    private String institutionSubjectType;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 证书类型，2:教师资格证,3:专业性学历证书,4:专业认证 */
    @Excel(name = "证书类型，2:教师资格证,3:专业性学历证书,4:专业认证")
    private Long certificateType;

    /** 证书图片路径链接 */
    @Excel(name = "证书图片路径链接")
    private String certificatePath;

    /** 证书有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "证书有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String certificateExpireTime;

    /** 授权函图片路径链接 */
    @Excel(name = "授权函图片路径链接")
    private String letterOfAuthorizationPath;

    /** 授权函有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "授权函有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String letterExpireTime;

    /** 机构类目-老师身份证正面链接,人像面 */
    @Excel(name = "机构类目-老师身份证正面链接,人像面")
    private String classTeacherFrontPath;

    /** 机构类目-老师身份反面链接,国徽面 */
    @Excel(name = "机构类目-老师身份反面链接,国徽面")
    private String classTeacherBackPath;

    /** 机构类目-老师姓名 */
    @Excel(name = "机构类目-老师姓名")
    private String classTeacherName;

    /** 机构类目-老师身份证号码 */
    @Excel(name = "机构类目-老师身份证号码")
    private String classTeacherIdNumber;

    /** 机构类目-老师身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构类目-老师身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String classTeacherIdCardExpireTime;

    /** 机构类目-合作声明图片路径链接 */
    @Excel(name = "机构类目-合作声明图片路径链接")
    private String cooperationStatementPath;

    /** 机构类目-合作声明有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构类目-合作声明有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String cooperationStatementExpireTime;

    /** 审核状态 0审核中，1审核通过，-1审核拒绝 */
    @Excel(name = "审核状态 0审核中，1审核通过，-1审核拒绝")
    private Integer auditStatus;

    /** 定时任务状态，0未开始，1已推送，2已回调 */
    @Excel(name = "定时任务状态，0未开始，1已推送，2已回调")
    private Integer jobStatus;

    /** 抖音审核拒绝消息 */
    @Excel(name = "抖音审核拒绝消息")
    private String douyinAuditMessage;

    /** 提交审核后抖音基本信息审核id */
    @Excel(name = "提交审核后抖音基本信息审核id")
    private String basicAuthTaskid;

    /** 提交审核后抖音类目信息审核id */
    @Excel(name = "提交审核后抖音类目信息审核id")
    private String classAuthTaskid;

    /** 提交审核后抖音生成的实体id */
    @Excel(name = "提交审核后抖音生成的实体id")
    private String entityId;

    /** 抖音提交审核错误信息 */
    @Excel(name = "抖音提交审核错误信息")
    private String errMsg;

    /** 抖音提交审核错误code */
    @Excel(name = "抖音提交审核错误code")
    private Long errCode;

    /** 0未授权,1已授权 */
    @Excel(name = "0未授权,1已授权")
    private Integer authRoleStatus;

    private String shopName;
    private String shopTeacherDesc;
    private String avatarUrl;
    private String mobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getEntityType() {
        return entityType;
    }

    public void setEntityType(Long entityType) {
        this.entityType = entityType;
    }

    public String getFrontPath() {
        return frontPath;
    }

    public void setFrontPath(String frontPath) {
        this.frontPath = frontPath;
    }

    public String getBackPath() {
        return backPath;
    }

    public void setBackPath(String backPath) {
        this.backPath = backPath;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getTeacherRealName() {
        return teacherRealName;
    }

    public void setTeacherRealName(String teacherRealName) {
        this.teacherRealName = teacherRealName;
    }

    public String getIdCardExpireTime() {
        return idCardExpireTime;
    }

    public void setIdCardExpireTime(String idCardExpireTime) {
        this.idCardExpireTime = idCardExpireTime;
    }

    public String getAvatarPath() {
        return avatarPath;
    }

    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getTeacherDesc() {
        return teacherDesc;
    }

    public void setTeacherDesc(String teacherDesc) {
        this.teacherDesc = teacherDesc;
    }

    public String getBusinessLicenseNo() {
        return businessLicenseNo;
    }

    public void setBusinessLicenseNo(String businessLicenseNo) {
        this.businessLicenseNo = businessLicenseNo;
    }

    public String getBusinessLicenseExpireTime() {
        return businessLicenseExpireTime;
    }

    public void setBusinessLicenseExpireTime(String businessLicenseExpireTime) {
        this.businessLicenseExpireTime = businessLicenseExpireTime;
    }

    public String getBusinessLicenseCompanyName() {
        return businessLicenseCompanyName;
    }

    public void setBusinessLicenseCompanyName(String businessLicenseCompanyName) {
        this.businessLicenseCompanyName = businessLicenseCompanyName;
    }

    public String getBusinessLicensePath() {
        return businessLicensePath;
    }

    public void setBusinessLicensePath(String businessLicensePath) {
        this.businessLicensePath = businessLicensePath;
    }

    public String getLegalPersonFrontPath() {
        return legalPersonFrontPath;
    }

    public void setLegalPersonFrontPath(String legalPersonFrontPath) {
        this.legalPersonFrontPath = legalPersonFrontPath;
    }

    public String getLegalPersonBackPath() {
        return legalPersonBackPath;
    }

    public void setLegalPersonBackPath(String legalPersonBackPath) {
        this.legalPersonBackPath = legalPersonBackPath;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLegalPersonIdNumber() {
        return legalPersonIdNumber;
    }

    public void setLegalPersonIdNumber(String legalPersonIdNumber) {
        this.legalPersonIdNumber = legalPersonIdNumber;
    }

    public String getLegalPersonIdCardExpireTime() {
        return legalPersonIdCardExpireTime;
    }

    public void setLegalPersonIdCardExpireTime(String legalPersonIdCardExpireTime) {
        this.legalPersonIdCardExpireTime = legalPersonIdCardExpireTime;
    }

    public String getInstitutionSceneType() {
        return institutionSceneType;
    }

    public void setInstitutionSceneType(String institutionSceneType) {
        this.institutionSceneType = institutionSceneType;
    }

    public String getInstitutionSubjectType() {
        return institutionSubjectType;
    }

    public void setInstitutionSubjectType(String institutionSubjectType) {
        this.institutionSubjectType = institutionSubjectType;
    }

    public Long getFirstClassId() {
        return firstClassId;
    }

    public void setFirstClassId(Long firstClassId) {
        this.firstClassId = firstClassId;
    }

    public Long getFirstClassPid() {
        return firstClassPid;
    }

    public void setFirstClassPid(Long firstClassPid) {
        this.firstClassPid = firstClassPid;
    }

    public String getFirstClassTitle() {
        return firstClassTitle;
    }

    public void setFirstClassTitle(String firstClassTitle) {
        this.firstClassTitle = firstClassTitle;
    }

    public Long getFirstClassDouyinClassId() {
        return firstClassDouyinClassId;
    }

    public void setFirstClassDouyinClassId(Long firstClassDouyinClassId) {
        this.firstClassDouyinClassId = firstClassDouyinClassId;
    }

    public Long getSecondClassId() {
        return secondClassId;
    }

    public void setSecondClassId(Long secondClassId) {
        this.secondClassId = secondClassId;
    }

    public Long getSecondClassPid() {
        return secondClassPid;
    }

    public void setSecondClassPid(Long secondClassPid) {
        this.secondClassPid = secondClassPid;
    }

    public String getSecondClassTitle() {
        return secondClassTitle;
    }

    public void setSecondClassTitle(String secondClassTitle) {
        this.secondClassTitle = secondClassTitle;
    }

    public Long getSecondClassDouyinClassId() {
        return secondClassDouyinClassId;
    }

    public void setSecondClassDouyinClassId(Long secondClassDouyinClassId) {
        this.secondClassDouyinClassId = secondClassDouyinClassId;
    }

    public Long getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Long certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificatePath() {
        return certificatePath;
    }

    public void setCertificatePath(String certificatePath) {
        this.certificatePath = certificatePath;
    }

    public String getCertificateExpireTime() {
        return certificateExpireTime;
    }

    public void setCertificateExpireTime(String certificateExpireTime) {
        this.certificateExpireTime = certificateExpireTime;
    }

    public String getLetterOfAuthorizationPath() {
        return letterOfAuthorizationPath;
    }

    public void setLetterOfAuthorizationPath(String letterOfAuthorizationPath) {
        this.letterOfAuthorizationPath = letterOfAuthorizationPath;
    }

    public String getLetterExpireTime() {
        return letterExpireTime;
    }

    public void setLetterExpireTime(String letterExpireTime) {
        this.letterExpireTime = letterExpireTime;
    }

    public String getClassTeacherFrontPath() {
        return classTeacherFrontPath;
    }

    public void setClassTeacherFrontPath(String classTeacherFrontPath) {
        this.classTeacherFrontPath = classTeacherFrontPath;
    }

    public String getClassTeacherBackPath() {
        return classTeacherBackPath;
    }

    public void setClassTeacherBackPath(String classTeacherBackPath) {
        this.classTeacherBackPath = classTeacherBackPath;
    }

    public String getClassTeacherName() {
        return classTeacherName;
    }

    public void setClassTeacherName(String classTeacherName) {
        this.classTeacherName = classTeacherName;
    }

    public String getClassTeacherIdNumber() {
        return classTeacherIdNumber;
    }

    public void setClassTeacherIdNumber(String classTeacherIdNumber) {
        this.classTeacherIdNumber = classTeacherIdNumber;
    }

    public String getClassTeacherIdCardExpireTime() {
        return classTeacherIdCardExpireTime;
    }

    public void setClassTeacherIdCardExpireTime(String classTeacherIdCardExpireTime) {
        this.classTeacherIdCardExpireTime = classTeacherIdCardExpireTime;
    }

    public String getCooperationStatementPath() {
        return cooperationStatementPath;
    }

    public void setCooperationStatementPath(String cooperationStatementPath) {
        this.cooperationStatementPath = cooperationStatementPath;
    }

    public String getCooperationStatementExpireTime() {
        return cooperationStatementExpireTime;
    }

    public void setCooperationStatementExpireTime(String cooperationStatementExpireTime) {
        this.cooperationStatementExpireTime = cooperationStatementExpireTime;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(Integer jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getDouyinAuditMessage() {
        return douyinAuditMessage;
    }

    public void setDouyinAuditMessage(String douyinAuditMessage) {
        this.douyinAuditMessage = douyinAuditMessage;
    }

    public String getBasicAuthTaskid() {
        return basicAuthTaskid;
    }

    public void setBasicAuthTaskid(String basicAuthTaskid) {
        this.basicAuthTaskid = basicAuthTaskid;
    }

    public String getClassAuthTaskid() {
        return classAuthTaskid;
    }

    public void setClassAuthTaskid(String classAuthTaskid) {
        this.classAuthTaskid = classAuthTaskid;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Long getErrCode() {
        return errCode;
    }

    public void setErrCode(Long errCode) {
        this.errCode = errCode;
    }

    public Integer getAuthRoleStatus() {
        return authRoleStatus;
    }

    public void setAuthRoleStatus(Integer authRoleStatus) {
        this.authRoleStatus = authRoleStatus;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopTeacherDesc() {
        return shopTeacherDesc;
    }

    public void setShopTeacherDesc(String shopTeacherDesc) {
        this.shopTeacherDesc = shopTeacherDesc;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
