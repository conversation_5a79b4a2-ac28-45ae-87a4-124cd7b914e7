package com.wendao101.common.core.kuaishou.course;

import lombok.Data;

/**
 * {
 *     "data": {
 *         "course_id": "kmc5199124422800784093",
 *         "approve_time": 1721197866015,  // 审核时间（毫秒时间戳）
 *         "version": 2,
 *         "status": 3  // 2 审核通过，3 审核驳回
 *     },
 *     "message_id": "e1f935bf-9c2d-426e-a330-598a5ddab82b",
 *     "event": "MP_COURSE_NOTIFY",
 *     "app_id": "ks694117293240723718",
 *     "timestamp": 1721197866025
 * }
 */
@Data
public class CourseUpdateCallbackDTO {
    private CourseUpdateCallbackDTOData data;
    private String message_id;
    private String event;
    private String app_id;
    private Long timestamp;

}
