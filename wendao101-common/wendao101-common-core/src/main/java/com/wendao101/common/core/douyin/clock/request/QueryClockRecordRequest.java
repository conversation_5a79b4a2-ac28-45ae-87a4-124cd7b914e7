package com.wendao101.common.core.douyin.clock.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class QueryClockRecordRequest {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

}
