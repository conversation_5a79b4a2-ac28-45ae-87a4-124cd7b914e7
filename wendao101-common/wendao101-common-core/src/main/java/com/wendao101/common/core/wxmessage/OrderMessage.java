package com.wendao101.common.core.wxmessage;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 参数长度限制
 * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html#%E7%B1%BB%E7%9B%AE%E6%A8%A1%E6%9D%BF%E6%B6%88%E6%81%AF
 */
@Data
public class OrderMessage {
    /**
     * 订单编号
     * {{character_string2.DATA}}
     * 商品名称
     * {{thing3.DATA}}
     * 支付方式
     * {{phrase5.DATA}}
     * 支付时间
     * {{time6.DATA}}
     * 实付金额
     * {{amount9.DATA}}
     */
    private Long teacherId;
    private String orderId;
    private String courseTitle;
    private String payWay;
    private Date payTime;
    private BigDecimal payPrice;

    private String ktTemplateId = "d0HIlVU1UMMU446SHjGTMSbzoAtSpaLqItLpxo5EJAA";
    private String hkTemplateId = "eqjoHk082SKBlw1ctOW0kNBlttSL8IjEYZ4rhmE8KQA";

    public String getCourseTitle() {
        if (courseTitle == null) {
            return null;
        }
        //20个以内字符	可汉字、数字、字母或符号组合
        return courseTitle.length() > 20 ? courseTitle.substring(0, 20) : courseTitle;
    }


}
