package com.wendao101.common.core.kuaishou.pay.child;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PaymentInfo {
    /**
     * 预下单用户支付金额
     */
    private int total_amount;
    /**
     * 支付状态。 取值： PROCESSING-处理中｜SUCCESS-成功｜FAILED-失败 TIMEOUT-超时
     */
    private String pay_status;
    /**
     * 订单支付时间，单位为毫秒时间戳。
     */
    private long pay_time;
    /**
     * 支付渠道。取值：UNKNOWN - 未知｜WECHAT-微信 ｜ALIPAY-支付宝。(注：如果用户还未支付，这里返回的是UNKNOWN.）
     */
    private String pay_channel;
    /**
     * 开发者下单单号
     */
    private String out_order_no;
    /**
     * 快手小程序平台订单号
     */
    private String ks_order_no;
    /**
     * 订单来源信息，历史订单为""
     */
    private String extra_info;
    /**
     * 是否参与分销，true:分销，false:非分销
     */
    private boolean enable_promotion;
    /**
     * 预计分销金额，单位：分
     */
    private int promotion_amount;
    /**
     * 订单对应的用户open id
     */
    private String open_id;
    /**
     * 开发者回传的订单同步状态，状态值说明见订单同步接口
     */
    private int order_status;

    /**
     * 获取extra_info中的信息
     * @return
     */
    public PaymentInfoExtraInfo fetchPaymentInfoExtraInfo(){
        if(StringUtils.isBlank(this.extra_info)){
            return null;
        }
        return JSON.parseObject(this.extra_info, PaymentInfoExtraInfo.class);
    }
}
