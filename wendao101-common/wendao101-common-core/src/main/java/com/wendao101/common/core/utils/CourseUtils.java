package com.wendao101.common.core.utils;

import java.util.Random;

public class CourseUtils {

    public static String generateIdNumber() {
        //创建一个随机数生成器
        Random random = new Random();
        //创建一个字符串缓冲区
        StringBuilder sb = new StringBuilder();
        //生成第一位数字，不能为0
        int first = random.nextInt(9) + 1;
        //将第一位数字添加到字符串缓冲区
        sb.append(first);
        //循环生成剩余的6位数字
        for (int i = 0; i < 6; i++) {
            //生成一个0到9的随机数
            int num = random.nextInt(10);
            //将随机数添加到字符串缓冲区
            sb.append(num);
        }
        //将字符串缓冲区转换为字符串
        String result = sb.toString();
        return result;
    }
}
