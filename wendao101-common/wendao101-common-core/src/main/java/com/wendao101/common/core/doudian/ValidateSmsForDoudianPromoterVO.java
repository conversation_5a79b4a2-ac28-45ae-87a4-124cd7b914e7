package com.wendao101.common.core.doudian;

import lombok.Data;

@Data
public class ValidateSmsForDoudianPromoterVO {
    private String phoneNumber;
    private String code;
    private String uuid;

    /**
     * 抖店达人uid
     */
    private String douyinUid;
    /**
     * 抖音openid
     */
    private String openId;

    /**
     * 所在的app,1问到好课,2问到课堂
     */
    private Integer appNameType;

    /**
     * 视频号 按手机号码查询出来的 用户Id
     */
    private Long userId;

    /**
     * 推广员姓名
     */
    private String promoterName;


    /**
     * 二维码扫码带入,在参数上
     */
    private Long teacherId;

    /**
     * requestId
     */
    private String requestId;
}
