package com.wendao101.common.core;

import lombok.Data;

import java.util.List;

@Data
public class LiveCourseRelation {
    public static final String LIVE_COURSE_RELATION_KEY = "liveCourseRelation:";
    //直播id
    //private Long liveId;
    //课程id列表
    private List<Long> courseIds;
    //权限
    // NoPermission(1,"无权限"),All(2,"所有课程"),DesignatedCourses(3,"指定课程");
    private Integer courseLivePermission = 1;
    //liveCourseRelation（新增）
    //courseIds（新增）
    //courseLivePermission（新增）
}
