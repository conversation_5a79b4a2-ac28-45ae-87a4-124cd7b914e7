package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *     "GroupId":"@TGS#a6I4ZUUGO",
 *     "CommandType": 1,
 *     "Admin_Account": ["user1"]
 * }
 */
@Data
public class ModifyAdminDTO {
    /**
     * 操作的群 ID
     */
    @JsonProperty("GroupId")
    @JSONField(name = "GroupId")
    String groupId;
    /**
     * 1: 设置管理员
     * 2: 取消设置管理员
     */
    @JsonProperty("CommandType")
    @JSONField(name = "CommandType")
    Integer commandType;
    /**
     * 要修改的管理员 UserID 列表，一个直播群最多可以设置5个管理员
     */
    @JsonProperty("Admin_Account")
    @JSONField(name = "Admin_Account")
    String[] adminAccount;

}
