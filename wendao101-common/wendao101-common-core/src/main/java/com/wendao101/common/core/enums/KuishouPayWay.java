package com.wendao101.common.core.enums;

public enum KuishouPayWay {
    UNKNOWN("UNKNOWN", "未知", "未知"),
    WECHAT("WECHAT", "微信", "微信支付"),
    ALIPAY("ALIPAY", "支付宝", "支付宝支付");

    private final String code;
    private final String ksName;

    private final String wendaoName;

    KuishouPayWay(String code, String ksName, String wendaoName) {
        this.code = code;
        this.ksName = ksName;
        this.wendaoName = wendaoName;
    }

    public String getCode() {
        return code;
    }

    public String getKsName() {
        return ksName;
    }

    public String getWendaoName() {
        return wendaoName;
    }

    public static KuishouPayWay getEnum(String code) {
        for (KuishouPayWay payWay : KuishouPayWay.values()) {
            if (payWay.getCode().equals(code)) {
                return payWay;
            }
        }
        return KuishouPayWay.UNKNOWN;
    }
}
