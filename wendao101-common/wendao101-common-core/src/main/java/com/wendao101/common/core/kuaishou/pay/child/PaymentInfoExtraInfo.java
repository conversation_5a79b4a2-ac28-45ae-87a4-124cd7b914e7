package com.wendao101.common.core.kuaishou.pay.child;

import lombok.Data;

@Data
public class PaymentInfoExtraInfo {
    /**
     * 视频/直播对应的链接：如为直播，返回为空；如为短视频，返回的是加密的视频ID，
     * 开发者可通过拼接http前缀，访问到具体视频。如短视频场景返回的URL为：3xqxmjkthzpckus；
     * 在该返回结果前拼接 https://www.kuaishou.com/short-video/
     * 生成：https://www.kuaishou.com/short-video/3xqxmjkthzpckus （可直接访问到具体视频）
     */
    private String url;
    /**
     * VIDEO=短视频 LIVE=直播
     */
    private String item_type;
    /**
     * 直播id或视频id
     */
    private String item_id;
    /**
     * 快手ID（ 注意快手ID区别于快手号，但对于具体账号，均唯一 )
     */
    private String author_id;
    /**
     * 用户侧支付页交易单号，具体获取方法可点击查看
     */
    private String trade_no;
}
