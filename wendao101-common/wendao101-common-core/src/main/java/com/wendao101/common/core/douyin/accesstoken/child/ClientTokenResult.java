package com.wendao101.common.core.douyin.accesstoken.child;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClientTokenResult {
    // client_token 接口调用凭证
    @JsonProperty("access_token")
    private String accessToken;
    // 错误码描述
    @JsonProperty("description")
    private String description;
    // 错误码
    @JsonProperty("error_code")
    private Long errorCode;
    // client_token 接口调用凭证超时时间，单位（秒）
    @JsonProperty("expires_in")
    private Long expiresIn;
}
