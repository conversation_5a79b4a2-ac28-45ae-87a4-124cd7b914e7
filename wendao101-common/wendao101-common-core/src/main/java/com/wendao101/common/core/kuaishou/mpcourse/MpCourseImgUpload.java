package com.wendao101.common.core.kuaishou.mpcourse;

import lombok.Data;

import java.net.URLEncoder;


public class MpCourseImgUpload {
    private String imgUrl;

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        //获取文件名称和文件名前面部分,并对文件名进行urlEncode,再组合起来
        String prefix = imgUrl.substring(0, imgUrl.lastIndexOf("/") + 1);
        String fileName = imgUrl.substring(imgUrl.lastIndexOf("/") + 1);
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.imgUrl = prefix+fileName;
    }
}
