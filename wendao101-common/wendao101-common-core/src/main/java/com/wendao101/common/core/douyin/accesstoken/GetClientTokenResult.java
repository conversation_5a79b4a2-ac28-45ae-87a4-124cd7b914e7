package com.wendao101.common.core.douyin.accesstoken;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wendao101.common.core.douyin.accesstoken.child.Extra;
import com.wendao101.common.core.douyin.accesstoken.child.ClientTokenResult;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * 错误码            描述                                          排查建议
 * error_code:10002 description:参数错误                           检查参数是否漏传
 * error_code:10003 description:client_key 不存在                  检查 client_key 参数是否正确
 * error_code:10013 description:client_key 或者 client_secret 报错  检查 client_key 和 client_secret 是否正确
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetClientTokenResult {
    @JsonProperty("data")
    private ClientTokenResult data;
    @JsonProperty("message")
    private String message;
    @JsonProperty("extra")
    private Extra extra;
}

