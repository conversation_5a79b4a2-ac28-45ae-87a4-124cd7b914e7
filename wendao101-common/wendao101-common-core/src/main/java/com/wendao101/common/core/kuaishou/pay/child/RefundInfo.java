package com.wendao101.common.core.kuaishou.pay.child;

import lombok.Data;

@Data
public class RefundInfo {
    /**
     * 快手小程序平台订单号
     */
    private String ks_order_no;
    /**
     * 退款状态。 REFUND_PROCESSING-处理中，REFUND_SUCCESS-成功，REFUND_FAILED-失败
     */
    private String refund_status;
    /**
     * 小程序平台的退款单号
     */
    private String refund_no;
    /**
     * 退款账户说明
     */
    private String ks_refund_type;
    /**
     * 此次退款金额。单位为分
     */
    private int refund_amount;
    /**
     * 退款失败原因
     */
    private String ks_refund_fail_reason;
    /**
     * 订单发起退款的原因
     */
    private String apply_refund_reason;
    /**
     * 快手小程序平台退款单号
     */
    private String ks_refund_no;


}