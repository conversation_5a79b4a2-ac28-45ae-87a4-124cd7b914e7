package com.wendao101.common.core.douyin.clock;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 打卡记录对象 clock_record
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockRecordResultData extends BaseEntity {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 作业id
     */
    @Excel(name = "作业id")
    private Long clockWorkId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡天数
     */
    @Excel(name = "打卡天数")
    private Integer clockDay;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clockTime;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    /**
     * 回复状态 0未回复 1已回复
     */
    @Excel(name = "回复状态 0未回复 1已回复")
    private Integer replyStatus;

    /**
     * 是否隐藏记录  0否 1是
     */
    @Excel(name = "是否隐藏记录  0否 1是")
    private Integer hideRecordType;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 作业图片
     */
    @Excel(name = "作业图片")
    private String workImgUrl;

    /**
     * 作业视频
     */
    @Excel(name = "作业视频")
    private String workVideoUrl;

    /**
     * 作业音频
     */
    @Excel(name = "作业音频")
    private String workAudioUrl;

    /**
     * 作业名称
     */
    @Excel(name = "作业名称")
    private String workTitle;
}
