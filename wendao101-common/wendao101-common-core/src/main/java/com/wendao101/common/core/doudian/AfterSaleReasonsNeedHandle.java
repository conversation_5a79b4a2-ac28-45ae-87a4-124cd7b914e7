package com.wendao101.common.core.doudian;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AfterSaleReasonsNeedHandle {
    private static final List<String> reasonList = new ArrayList<String>() {{
        add("商品做工粗糙/有瑕疵");
        add("假冒品牌");
        add("大小/品种/产地等描述不符");
        add("商品腐烂/变质");
        add("盗版");
        add("做工瑕疵/品质差");
        add("做工粗糙/有瑕疵/有污渍");
        add("商家关店/装修/转让");
        add("商品质量不好");
        add("商品质量问题");
        add("商品质量问题（变质/发霉等）");
        add("商品质量问题（变质/死亡等）");
        add("商品质量问题（损坏/变形等）");
        add("商品质量问题（损坏/掉漆等）");
        add("商品质量问题（褪色/掉色/发黑等）");
        add("商品质量问题（异味/变质等）");
        add("商品质量问题（有异物/变质等）");
        add("大小／尺寸／重量与商品描述不符");
        add("生产日期／保质期与商品描述不符");
        add("品种／规格／成分等与商品描述不符");
        add("卡券无法使用");
        add("商品与描述不符");
        add("商品与页面描述不符");
        add("颜色/图案/款式与商品描述不符");
        add("成分/材质与商品描述不符");
        add("课程进度与页面描述不符");
        add("食用后身体不适");
        add("成分造假/饮用后不适");
        add("材质/成分造假");
        add("宠物状态不好/死亡");
        add("商品腐烂/死亡/残断");
        add("商品效果不好");
        //add("商品破损/包装问题");
//        add("包装/商品破损");
//        add("商品漏发配件/赠品");
//        add("商品漏发配件/赠品/证书");
//        add("商品/赠品/配件/证书漏发");
//        add("赠品漏发/少发");
//        add("少件／漏发");
        //add("少件/漏发");
        //add("商家发错货");
        //add("未按规定时间送达");
        add("服务态度不好");
        //新增
        //add("未按约定时间发货");
        //add("虚假承诺发货时效");
        //add("发货慢");
        //add("商品缺货");
        //add("未收到课程/资料/服务等");
        add("课程无法观看/app无法下载");
        add("商品效果不好");
        add("提供的服务不符");
        add("课程进度与描述不符");
        add("生产日期/保质期不符");
        add("充值少到账");
        add("套餐/资费/期限/余额不符");
        add("生鲜卡券无法使用");
        //add("未收到课程/资料/服务等");
    }};

    public static List<String> fetchReasonList() {
        return reasonList;
    }
}
