package com.wendao101.common.core.douyin.clock.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QueryClockRecordResponse {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡天数
     */
    @Excel(name = "打卡天数")
    private Integer clockDay;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clockTime;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    /**
     * 打卡图片  （作业图片）
     */
    @Excel(name = "打卡图片  （作业图片） ")
    private String clockImg;

    /**
     * 打卡作业视频
     */
    @Excel(name = "打卡作业视频 ")
    private String clockVideo;

    /**
     * 打卡作业音频
     */
    @Excel(name = "打卡作业音频 ")
    private String clockAudio;

    /**
     * 打卡回复
     */
    @Excel(name = "打卡回复")
    private List<ClockReplyResponse> replyResponseList;

}
