package com.wendao101.common.core.aliyunlive.miclive.child;

import com.aliyuncs.aui.dto.res.RoomInfoDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VodInfo {
    private Integer status;

    @JsonProperty("playlist")
    private List<PlayInfo> playInfos;
}
