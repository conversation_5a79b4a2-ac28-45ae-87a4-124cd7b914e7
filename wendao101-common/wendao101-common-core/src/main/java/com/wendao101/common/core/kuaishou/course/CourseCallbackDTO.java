package com.wendao101.common.core.kuaishou.course;

import lombok.Data;

/**
 * {
 * "data": {
 * "app_id": "ks707065143182458884",
 * "current_time": 1718195302042,
 * "video_id": "af41cc28dcb81961",
 * "status": 2
 * },
 * "message_id": "0d41e36a-57a3-4244-9d81",
 * "event": "MP_VIDEO_EVENT",
 * "app_id": "ks707065143182458884",
 * "timestamp": 1718195302936
 * }
 * <p>
 * {
 * "data": {
 * "app_id": "ks707065143182458884",
 * "current_time": 1718195302042,
 * "audio_id": "af41cc28dcb81961",
 * "status": 2
 * },
 * "message_id": "0d41e36a-57a3-4244-9d81",
 * "event": "MP_AUDIO_EVENT",
 * "app_id": "ks707065143182458884",
 * "timestamp": 1718195302936
 * }
 */
@Data
public class CourseCallbackDTO {
    private CourseCallbackDTOData data;
    private String message_id;
    private String event;
    private String app_id;
    private Long timestamp;

}
