package com.wendao101.common.core.kuaishou.pay.child;

import lombok.Data;

@Data
public class SettleInfo {
    /**
     * 开发者的结算单号
     */
    private String settle_no;
    /**
     * 支付订单的总金额，单位为分
     */
    private int total_amount;
    /**
     * 结算后给商户的金额，单位为分
     */
    private int settle_amount;
    /**
     * SETTLE_PROCESSING-处理中，SETTLE_SUCCESS-成功，SETTLE_FAILED-失败
     */
    private String settle_status;
    /**
     * 快手小程序平台订单号
     */
    private String ks_order_no;
    /**
     * 快手小程序平台结算单号
     */
    private String ks_settle_no;
}
