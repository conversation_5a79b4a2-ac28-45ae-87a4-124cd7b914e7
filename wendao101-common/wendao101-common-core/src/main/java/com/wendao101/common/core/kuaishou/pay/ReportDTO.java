package com.wendao101.common.core.kuaishou.pay;

/**
 * Auto-generated: 2024-01-22 17:23:33
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class ReportDTO {

    private String out_order_no;
    private String out_biz_order_no;
    private String open_id;
    private long order_create_time;
    private int order_status;
    private String order_path;
    private String order_backup_url;
    private String product_cover_img_id;
    private String poi_id;
    private String product_id;
    private int product_catalog_code;
    private String product_city;
    public void setOut_order_no(String out_order_no) {
        this.out_order_no = out_order_no;
    }
    public String getOut_order_no() {
        return out_order_no;
    }

    public void setOut_biz_order_no(String out_biz_order_no) {
        this.out_biz_order_no = out_biz_order_no;
    }
    public String getOut_biz_order_no() {
        return out_biz_order_no;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }
    public String getOpen_id() {
        return open_id;
    }

    public void setOrder_create_time(long order_create_time) {
        this.order_create_time = order_create_time;
    }
    public long getOrder_create_time() {
        return order_create_time;
    }

    public void setOrder_status(int order_status) {
        this.order_status = order_status;
    }
    public int getOrder_status() {
        return order_status;
    }

    public void setOrder_path(String order_path) {
        this.order_path = order_path;
    }
    public String getOrder_path() {
        return order_path;
    }

    public void setOrder_backup_url(String order_backup_url) {
        this.order_backup_url = order_backup_url;
    }
    public String getOrder_backup_url() {
        return order_backup_url;
    }

    public void setProduct_cover_img_id(String product_cover_img_id) {
        this.product_cover_img_id = product_cover_img_id;
    }
    public String getProduct_cover_img_id() {
        return product_cover_img_id;
    }

    public void setPoi_id(String poi_id) {
        this.poi_id = poi_id;
    }
    public String getPoi_id() {
        return poi_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }
    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_catalog_code(int product_catalog_code) {
        this.product_catalog_code = product_catalog_code;
    }
    public int getProduct_catalog_code() {
        return product_catalog_code;
    }

    public void setProduct_city(String product_city) {
        this.product_city = product_city;
    }
    public String getProduct_city() {
        return product_city;
    }

}