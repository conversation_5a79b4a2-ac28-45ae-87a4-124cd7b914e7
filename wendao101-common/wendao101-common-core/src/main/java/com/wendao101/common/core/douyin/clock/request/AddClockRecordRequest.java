package com.wendao101.common.core.douyin.clock.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class AddClockRecordRequest {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 平台open_id
     */
    @Excel(name = "平台open_id")
    private String openId;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 作业id
     */
    @Excel(name = "作业id")
    private Long clockWorkId;

    /**
     * 打卡id
     */
    @Excel(name = "打卡id")
    private Long clockInQuestId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡天数
     */
    @Excel(name = "打卡天数")
    private Integer clockDay;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clockTime;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    @Excel(name = "0打卡 1作业")
    private Integer clockType;

    /**
     * 打卡图片  （作业图片）
     */
    @Excel(name = "打卡图片  （作业图片） ")
    private String clockImg;

    /**
     * 打卡作业视频
     */
    @Excel(name = "打卡作业视频 ")
    private String clockVideo;

    /**
     * 打卡作业音频
     */
    @Excel(name = "打卡作业音频 ")
    private String clockAudio;


    private Integer appNameType=1;

}
