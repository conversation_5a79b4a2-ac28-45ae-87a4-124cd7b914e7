package com.wendao101.common.core.douyin.accesstoken;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAccessTokenDTO {

    // 小程序 ID
    @JsonProperty("appid")
    private String appid;

    // 小程序的 APP Secret，可以在开发者后台获取
    @JsonProperty("secret")
    private String secret;

    // 获取 access_token 时值为 client_credential
    @JsonProperty("grant_type")
    private String grantType;

}

