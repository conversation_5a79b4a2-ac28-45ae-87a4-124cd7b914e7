package com.wendao101.common.core.web.page;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class TableDataInfoWithTotalMoneyAndSubMoney implements Serializable{

    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据 */
    private List<?> rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;


    private BigDecimal totalSubMoney;
    private BigDecimal totalSaleMoney;


    public BigDecimal getTotalSubMoney() {
        return totalSubMoney;
    }

    public void setTotalSubMoney(BigDecimal totalSubMoney) {
        this.totalSubMoney = totalSubMoney;
    }

    public BigDecimal getTotalSaleMoney() {
        return totalSaleMoney;
    }

    public void setTotalSaleMoney(BigDecimal totalSaleMoney) {
        this.totalSaleMoney = totalSaleMoney;
    }

    /**
     * 表格数据对象
     */
    public TableDataInfoWithTotalMoneyAndSubMoney()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfoWithTotalMoneyAndSubMoney(List<?> list, int total)
    {
        this.rows = list;
        this.total = total;
    }

    public static TableDataInfo error(String msg){
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setCode(500);
        tableDataInfo.setMsg(msg);
        return tableDataInfo;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public List<?> getRows()
    {
        return rows;
    }

    public void setRows(List<?> rows)
    {
        this.rows = rows;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }
}
