package com.wendao101.common.core.douyin.clock;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 打卡作业对象 clock_work
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockWorkResponse {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 打卡任务id
     */
    @Excel(name = "打卡任务id")
    private Long clockInQuestId;

    /**
     * 作业标题
     */
    @Excel(name = "作业标题")
    private String workTitle;

    /**
     * 作业内容
     */
    @Excel(name = "作业内容")
    private String workContent;

    /**
     * 作业图片
     */
    @Excel(name = "作业图片")
    private String workImgUrl;

    /**
     * 作业视频封面图
     */
    @Excel(name = "作业视频封面图")
    private String workVideoImg;

    /**
     * 作业视频
     */
    @Excel(name = "作业视频")
    private String workVideoUrl;

    /**
     * 作业音频
     */
    @Excel(name = "作业音频")
    private String workAudioUrl;

    /**
     * 示例作业图片
     */
    @Excel(name = "示例作业图片")
    private String exampleWorkImgUrl;

    /**
     * 示例作业视频封面图
     */
    @Excel(name = "示例作业视频封面图")
    private String exampleWorkVideoImg;

    /**
     * 示例作业视频
     */
    @Excel(name = "示例作业视频")
    private String exampleWorkVideoUrl;

    /**
     * 示例作业音频
     */
    @Excel(name = "示例作业音频")
    private String exampleWorkAudioUrl;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否已经完成作业 0否 1是
     */
    private Integer status;
}
