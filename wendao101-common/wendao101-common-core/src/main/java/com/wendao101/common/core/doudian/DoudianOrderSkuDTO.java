package com.wendao101.common.core.doudian;

import com.wendao101.common.core.xhs.common.SkuOrderInfo;
import lombok.Data;

import java.util.List;

@Data
public class DoudianOrderSkuDTO {
    //总订单
    private String orderId;
    private List<SkuOrderInfo> skuInfoList;
    private Integer totalPayAmount;
    private String doudianUserId;
    //收件人电话,加密的数据!
    private String postTel;
    //收件人姓名
    private String postReceiver;
    //收件人详细地址:
    //省/直辖市
    private String postAddrProvince;
    //市
    private String postAddrCity;
    //区县
    private String ostAddrTown;
    //街道
    private String ostAddrStreet;
    //详细地址
    private String ostAddrDetail;
    //0、普通订单 2、虚拟商品订单 4、电子券（poi核销） 5、三方核销 6、服务市场
    private Long orderType;

    /**
     * 额；7=无需支付（0元单）；8=DOU分期（信用支付）；9=新卡支付；12=先用后付；16=收银台支付
     */
    private String payWay;

    //抖店用
    private List<Long> sIds;

    //private String postTel;

    private Long shopId;
}
