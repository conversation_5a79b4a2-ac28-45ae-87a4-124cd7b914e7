package com.wendao101.common.core.utils.livechat;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

@Slf4j
public class ImPostUtil {
    /**
     * 账号管理
     *
     * 功能说明
     * 接口
     * 导入单个账号
     * v4/im_open_login_svc/account_import
     *
     * 导入多个账号
     * v4/im_open_login_svc/multiaccount_import
     *
     * 删除账号
     * v4/im_open_login_svc/account_delete
     *
     * 查询账号
     * v4/im_open_login_svc/account_check
     *
     * 失效账号登录状态
     * v4/im_open_login_svc/kick
     *
     * 查询账号在线状态
     * v4/openim/query_online_status
     *
     *
     * 单聊消息
     *
     * 功能说明
     * 接口
     * 单发单聊消息
     *
     * v4/openim/sendmsg
     *
     * 批量发单聊消息
     *
     * v4/openim/batchsendmsg
     *
     * 导入单聊消息
     *
     * v4/openim/importmsg
     *
     * 查询单聊消息
     *
     * v4/openim/admin_getroammsg
     *
     * 撤回单聊消息
     *
     * v4/openim/admin_msgwithdraw
     *
     * 设置单聊消息已读
     *
     * v4/openim/admin_set_msg_read
     *
     * 查询单聊未读消息计数
     *
     * v4/openim/get_c2c_unread_msg_num
     *
     * 修改单聊历史消息
     *
     * v4/openim/modify_c2c_msg
     *
     *
     *
     * 资料管理
     *
     * 功能说明
     * 接口
     * 设置资料
     *
     * v4/profile/portrait_set
     *
     * 拉取资料
     *
     * v4/profile/portrait_get
     *
     *
     *
     * 关系链管理
     *
     * 功能说明
     * 接口
     * 添加好友
     *
     * v4/sns/friend_add
     *
     * 导入好友
     *
     * v4/sns/friend_import
     *
     * 更新好友
     *
     * v4/sns/friend_update
     *
     * 删除好友
     *
     * v4/sns/friend_delete
     *
     * 删除所有好友
     *
     * v4/sns/friend_delete_all
     *
     * 校验好友
     *
     * v4/sns/friend_check
     *
     * 拉取好友
     *
     * v4/sns/friend_get
     *
     * 拉取指定好友
     *
     * v4/sns/friend_get_list
     *
     * 添加黑名单
     *
     * v4/sns/black_list_add
     *
     * 删除黑名单
     *
     * v4/sns/black_list_delete
     *
     * 拉取黑名单
     *
     * v4/sns/black_list_get
     *
     * 校验黑名单
     *
     * v4/sns/black_list_check
     *
     * 添加分组
     *
     * v4/sns/group_add
     *
     * 删除分组
     *
     * v4/sns/group_delete
     *
     * 拉取分组
     *
     * v4/sns/group_get
     *
     *
     *
     * 最近联系人
     *
     * 功能说明
     * 接口
     * 拉取会话列表
     *
     * v4/recentcontact/get_list
     *
     * 删除单个会话
     *
     * v4/recentcontact/delete
     *
     * 创建会话分组数据
     *
     * v4/recentcontact/create_contact_group
     *
     * 删除会话分组数据
     *
     * v4/recentcontact/del_contact_group
     *
     * 更新会话分组数据
     *
     * v4/recentcontact/update_contact_group
     *
     * 搜索会话分组标记数据
     *
     * v4/recentcontact/search_contact_group
     *
     * 创建或更新会话标记数据
     *
     * v4/recentcontact/mark_contact
     *
     * 拉取会话分组标记数据
     *
     * v4/recentcontact/get_contact_group
     *
     *
     *
     * 群组管理
     *
     * 功能说明
     * 接口
     * 获取 App 中的所有群组
     *
     * v4/group_open_http_svc/get_appid_group_list
     *
     * 创建群组
     *
     * v4/group_open_http_svc/create_group
     *
     * 获取群详细资料
     *
     * v4/group_open_http_svc/get_group_info
     *
     * 获取群成员详细资料
     *
     * v4/group_open_http_svc/get_group_member_info
     *
     * 修改群基础资料
     *
     * v4/group_open_http_svc/modify_group_base_info
     *
     * 增加群成员
     *
     * v4/group_open_http_svc/add_group_member
     *
     * 删除群成员
     *
     * v4/group_open_http_svc/delete_group_member
     *
     * 修改群成员资料
     *
     * v4/group_open_http_svc/modify_group_member_info
     *
     * 解散群组
     *
     * v4/group_open_http_svc/destroy_group
     *
     * 获取用户所加入的群组
     *
     * v4/group_open_http_svc/get_joined_group_list
     *
     * 查询用户在群组中的身份
     *
     * v4/group_open_http_svc/get_role_in_group
     *
     * 批量禁言和取消禁言
     *
     * v4/group_open_http_svc/forbid_send_msg
     *
     * 获取被禁言群成员列表
     *
     * v4/group_open_http_svc/get_group_muted_account
     *
     * 在群组中发送普通消息
     *
     * v4/group_open_http_svc/send_group_msg
     *
     * 在群组中发送系统通知
     *
     * v4/group_open_http_svc/send_group_system_notification
     *
     * 撤回群消息
     *
     * v4/group_open_http_svc/group_msg_recall
     *
     * 转让群主
     *
     * v4/group_open_http_svc/change_group_owner
     *
     * 导入群基础资料
     *
     * v4/group_open_http_svc/import_group
     *
     * 导入群消息
     *
     * v4/group_open_http_svc/import_group_msg
     *
     * 导入群成员
     *
     * v4/group_open_http_svc/import_group_member
     *
     * 设置成员未读消息计数
     *
     * v4/group_open_http_svc/set_unread_msg_num
     *
     * 删除指定用户发送的消息
     *
     * v4/group_open_http_svc/delete_group_msg_by_sender
     *
     * 拉取群历史消息
     *
     * v4/group_open_http_svc/group_msg_get_simple
     *
     * 获取直播群在线人数
     *
     * v4/group_open_http_svc/get_online_member_num
     *
     * 获取直播群在线成员列表
     * v4/group_open_avchatroom_http_svc/get_members
     * 设置直播群成员标记
     * v4/group_open_avchatroom_http_svc/modify_user_info
     * 获取群自定义属性
     *
     * v4/group_open_attr_http_svc/get_group_attr
     *
     * 获取封禁群成员列表
     *
     * v4/group_open_http_svc/get_group_ban_member
     *
     * 群成员封禁
     *
     * v4/group_open_http_svc/ban_group_member
     *
     * 群成员解封
     *
     * v4/group_open_http_svc/unban_group_member
     *
     * 修改群自定义属性
     *
     * v4/group_open_http_svc/modify_group_attr
     *
     * 清空群自定义属性
     *
     * v4/group_open_http_svc/clear_group_attr
     *
     * 重置群自定义属性
     *
     * v4/group_open_http_svc/set_group_attr
     * 删除群自定义属性
     * v4/group_open_http_svc/delete_group_attr
     * 修改群聊历史消息
     *
     * v4/openim/modify_group_msg
     *
     * 直播群广播消息
     *
     * v4/group_open_http_svc/send_broadcast_msg
     *
     * 获取群计数器
     *
     * v4/group_open_http_svc/get_group_counter
     *
     * 更新群计数器
     *
     * v4/group_open_http_svc/update_group_counter
     *
     * 删除群计数器
     *
     * v4/group_open_http_svc/delete_group_counter
     *
     *
     *
     * 全局禁言管理
     *
     * 功能说明
     * 接口
     * 设置全局禁言
     *
     * v4/openconfigsvr/setnospeaking
     * 查询全局禁言
     *
     * v4/openconfigsvr/getnospeaking
     *
     *
     * 运营管理
     *
     * 功能说明
     * 接口
     * 拉取运营数据
     *
     * v4/openconfigsvr/getappinfo
     *
     * 下载消息记录
     *
     * v4/open_msg_svc/get_history
     *
     * 获取服务器 IP 地址
     *
     * v4/ConfigSvc/GetIPList
     *
     * 聊天文件封禁
     *
     * v4/im_cos_msg/forbid_illegal_object
     *
     * 聊天文件解封
     *
     * v4/im_cos_msg/allow_banned_object
     *
     * 聊天文件签名
     *
     * v4/im_cos_msg/get_cos_sig
     */
    ///v4/im_open_login_svc/account_import
    public static final String imBaseDomain = "https://console.tim.qq.com";
    public static final String imBackupDomain = "https://adminapi.my-imcloud.com";

    public static final String accountImport = "/v4/im_open_login_svc/account_import";
    public static final String portraitSet = "/v4/profile/portrait_set";
    public static final String createGroupUrl = "/v4/group_open_http_svc/create_group";
    public static final String getGroupInfoUrl = "/v4/group_open_http_svc/get_group_info";

    public static final String getGroupMemberInfoUrl = "/v4/group_open_http_svc/get_group_member_info";

    public static final String modifyGroupBaseInfoUrl = "/v4/group_open_http_svc/modify_group_base_info";

    public static final String addGroupMemberUrl = "/v4/group_open_http_svc/add_group_member";

    public static final String deleteGroupMemberUrl = "/v4/group_open_http_svc/delete_group_member";

    public static final String modifyGroupMemberInfoUrl = "/v4/group_open_http_svc/modify_group_member_info";

    public static final String destroyGroupUrl = "/v4/group_open_http_svc/destroy_group";

    public static final String getJoinedGroupListUrl = "/v4/group_open_http_svc/get_joined_group_list";

    public static final String getRoleInGroupUrl = "/v4/group_open_http_svc/get_role_in_group";

    public static final String forbidSendMsgUrl = "/v4/group_open_http_svc/forbid_send_msg";

    public static final String getGroupMutedAccountUrl = "/v4/group_open_http_svc/get_group_muted_account";

    public static final String sendGroupMsgUrl = "/v4/group_open_http_svc/send_group_msg";

    public static final String sendGroupSystemNoticeUrl = "/v4/group_open_http_svc/send_group_system_notification";

    public static final String recallGroupMsgUrl = "/v4/group_open_http_svc/group_msg_recall";

    public static final String getOnlineMemberNumUrl = "/v4/group_open_http_svc/get_online_member_num";

    ///v4/group_open_avchatroom_http_svc/modify_admin
    public static final String modifyAdminUrl = "/v4/group_open_avchatroom_http_svc/modify_admin";

    public static final String forbidSendMsg = "/v4/group_open_http_svc/forbid_send_msg";

    public static final String get_group_muted_account = "/v4/group_open_http_svc/get_group_muted_account";
    public static final String set_group_attr = "/v4/group_open_http_svc/set_group_attr";

    public static final String modify_group_attr = "/v4/group_open_http_svc/modify_group_attr";
    public static final String get_group_attrs = "/v4/group_open_attr_http_svc/get_group_attr";


    public static final Long sdkAppId = **********L;
    public static final String secret = "1de89d5d819788c2ae8ea4abb2bfe6ba893f2dc56ceacf783792656969ac5373";

    public static final int EXPIRETIME = 604800; // 7天




    public static final String queryInfo = "?sdkappid=**********&identifier=wendao&usersig=%s&random=%s&contenttype=json";



    public static <T, K> T sendPostData(K dto, String uri, Class<T> clazz) {
        String url = imBaseDomain + uri;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
}
