package com.wendao101.common.core.wxpaydto;


import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class WxCreateRefundOrderDTO {

    @NotNull
    @Excel(name = "微信订单号")
    private String outTradeNo;

    @NotNull
    private String openId;

    @NotNull
    @Excel(name = "退款原因")
    private String refundReason;

    @Excel(name = "补充描述")
    private String supplimentalDescription;

    @Excel(name = "凭证照片")
    private String receiptImg;

}
