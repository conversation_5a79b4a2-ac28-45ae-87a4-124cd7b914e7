package com.wendao101.common.core.douyin.clock;

import com.wendao101.common.core.douyin.clock.response.ClockInQuestDTO;
import lombok.Data;
import java.util.List;

/**
 * 打卡记录对象 clock_record
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockRecordResult {

    private List<ClockRecordResultData> clockRecordResultList;

    private ClockInQuestDTO clock;

    private List<ClockWorkResponse> clockWorkList;
}
