package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *           "key":"attr_key", //属性key
 *           "value":"attr_val" //属性value
 *       }
 */
@Data
public class GroupAttrDTO {
    @JsonProperty("key")
    @JSONField(name = "key")
    private String key;
    @JsonProperty("value")
    @JSONField(name = "value")
    private String value;
}
