package com.wendao101.common.core.kuaishou.pay;

import com.wendao101.common.core.kuaishou.pay.child.PaymentInfo;
import com.wendao101.common.core.kuaishou.pay.child.PaymentInfoExtraInfo;
import lombok.Data;

@Data
public class QueryOrderResult {
    /**
     * 状态码 1-业务处理成功
     */
    private int result;
    /**
     * 错误提示信息，常见错误处理可参考附录常见问题章节
     */
    private String error_msg;
    /**
     * 订单支付信息
     */
    private PaymentInfo payment_info;




}
