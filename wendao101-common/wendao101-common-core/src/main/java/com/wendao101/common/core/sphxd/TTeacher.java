package com.wendao101.common.core.sphxd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 老师对象 t_teacher
 * 
 * <AUTHOR>
 * @date 2023-09-02
 */
public class TTeacher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private boolean havaCertInfo = false;

    private Integer entityType;

    private Long certId;

    private Integer certAuditStatus;

    private String certDouyinAuditMessage;

    private String businessLicensePath;

    /** 主键id */
    private Long teacherId;




    /** unionId */
    @Excel(name = "unionId")
    private String unionId;

    /** openId */
    @Excel(name = "openId")
    private String openId;

    /** 店铺名称同时也是登录显示名，例如“淘气包” */
    @Excel(name = "店铺名称同时也是登录显示名，例如“淘气包”")
    private String shopName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobile;

    /** 登录次数 */
    @Excel(name = "登录次数")
    private Long loginNum;

    /** 密码（经过加密） */
    @Excel(name = "密码", readConverterExp = "经=过加密")
    private String password;

    /** 老师真实姓名 例如“刘德华” */
    @Excel(name = "老师真实姓名 例如“刘德华”")
    private String teacherName;

    /** 教师简介 */
    @Excel(name = "教师简介")
    private String teacherDesc;

    /** 教师头像url */
    @Excel(name = "教师头像url")
    private String avatarUrl;

    /** 所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开 */
    @Excel(name = "所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开")
    private String platform;

    /** 是否热门，0不是，1是 */
    @Excel(name = "是否热门，0不是，1是")
    private Integer hot;

    /** 最后登录IP */
    @Excel(name = "最后登录IP")
    private String loginIp;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginDate;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /** 抖音uid */
    @Excel(name = "抖音uid")
    private String dyUid;

    /** 性别(1 : 男 ， 0 : 女) */
    @Excel(name = "性别(1 : 男 ， 0 : 女)")
    private Integer gender;

    /** 版本1.免费版本,2.标准版本,3.企业版本 */
    @Excel(name = "版本1.免费版本,2.标准版本,3.企业版本")
    private Integer version;

    /** 版本到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "版本到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;



    private Integer appNameType;

    public Integer getAppNameType() {
        return appNameType;
    }

    public void setAppNameType(Integer appNameType) {
        this.appNameType = appNameType;
    }

    public Long getCertId() {
        return certId;
    }

    public void setCertId(Long certId) {
        this.certId = certId;
    }

    public Integer getCertAuditStatus() {
        return certAuditStatus;
    }

    public void setCertAuditStatus(Integer certAuditStatus) {
        this.certAuditStatus = certAuditStatus;
    }

    public String getCertDouyinAuditMessage() {
        return certDouyinAuditMessage;
    }

    public void setCertDouyinAuditMessage(String certDouyinAuditMessage) {
        this.certDouyinAuditMessage = certDouyinAuditMessage;
    }

    public boolean isHavaCertInfo() {
        return havaCertInfo;
    }

    public void setHavaCertInfo(boolean havaCertInfo) {
        this.havaCertInfo = havaCertInfo;
    }

    public Integer getEntityType() {
        return entityType;
    }

    public void setEntityType(Integer entityType) {
        this.entityType = entityType;
    }

    public String getBusinessLicensePath() {
        return businessLicensePath;
    }

    public void setBusinessLicensePath(String businessLicensePath) {
        this.businessLicensePath = businessLicensePath;
    }

    public void setTeacherId(Long teacherId)
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setUnionId(String unionId) 
    {
        this.unionId = unionId;
    }

    public String getUnionId() 
    {
        return unionId;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }
    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }
    public void setLoginNum(Long loginNum) 
    {
        this.loginNum = loginNum;
    }

    public Long getLoginNum() 
    {
        return loginNum;
    }
    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getPassword() 
    {
        return password;
    }
    public void setTeacherName(String teacherName) 
    {
        this.teacherName = teacherName;
    }

    public String getTeacherName() 
    {
        return teacherName;
    }
    public void setTeacherDesc(String teacherDesc) 
    {
        this.teacherDesc = teacherDesc;
    }

    public String getTeacherDesc() 
    {
        return teacherDesc;
    }
    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }
    public void setPlatform(String platform) 
    {
        this.platform = platform;
    }

    public String getPlatform() 
    {
        return platform;
    }
    public void setHot(Integer hot) 
    {
        this.hot = hot;
    }

    public Integer getHot() 
    {
        return hot;
    }
    public void setLoginIp(String loginIp) 
    {
        this.loginIp = loginIp;
    }

    public String getLoginIp() 
    {
        return loginIp;
    }
    public void setLoginDate(Date loginDate) 
    {
        this.loginDate = loginDate;
    }

    public Date getLoginDate() 
    {
        return loginDate;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setDyUid(String dyUid) 
    {
        this.dyUid = dyUid;
    }

    public String getDyUid() 
    {
        return dyUid;
    }
    public void setGender(Integer gender) 
    {
        this.gender = gender;
    }

    public Integer getGender() 
    {
        return gender;
    }
    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }
    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teacherId", getTeacherId())
            .append("unionId", getUnionId())
            .append("openId", getOpenId())
            .append("shopName", getShopName())
            .append("mobile", getMobile())
            .append("loginNum", getLoginNum())
            .append("password", getPassword())
            .append("teacherName", getTeacherName())
            .append("teacherDesc", getTeacherDesc())
            .append("avatarUrl", getAvatarUrl())
            .append("platform", getPlatform())
            .append("hot", getHot())
            .append("loginIp", getLoginIp())
            .append("loginDate", getLoginDate())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("dyUid", getDyUid())
            .append("gender", getGender())
            .append("version", getVersion())
            .append("endDate", getEndDate())
            .toString();
    }
}
