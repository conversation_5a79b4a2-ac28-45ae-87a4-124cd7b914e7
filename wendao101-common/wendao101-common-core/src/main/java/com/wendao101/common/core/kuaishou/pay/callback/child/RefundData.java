package com.wendao101.common.core.kuaishou.pay.callback.child;

import lombok.Data;

@Data
public class RefundData {
    /**
     * 开发者的退款单号。
     */
    private String out_refund_no;
    /**
     * 退款金额
     */
    private int refund_amount;
    /**
     * 预下单时携带的开发者自定义信息
     */
    private String attach;
    /**
     * 退款状态。 取值： PROCESSING-处理中，SUCCESS-成功，FAILED-失败
     */
    private String status;
    /**
     * 快手小程序平台订单号。
     */
    private String ks_order_no;
    /**
     * 快手小程序平台退款单号。
     */
    private String ks_refund_no;
    /**
     * 退款账户说明
     */
    private String ks_refund_type;
    /**
     * 	退款失败原因
     */
    private String ks_refund_fail_reason;
    /**
     * 订单发起退款的原因
     */
    private String apply_refund_reason;
}
