package com.wendao101.common.core.sphxd;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 视频号小店课程对象 sphxd_course
 * 
 * <AUTHOR>
 * @date 2023-11-15
 */
public class SphxdCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 微信视频号小店审核状态,-1审核不通过,0审核中,1审核通过,2编辑中 */
    @Excel(name = "微信视频号小店审核状态,-1审核不通过,0审核中,1审核通过,2编辑中")
    private Integer wxAuditStatus;

    /** 审核不通过原因 */
    @Excel(name = "审核不通过原因")
    private String wxAuditMsg;

    /** 1可售,-1不可售 */
    @Excel(name = "1可售,-1不可售")
    private Integer wxSaleStatus;

    /** 商品标题，最少3字符，最多60字符。文本内容规则请看注意事项 */
    @Excel(name = "商品标题，最少3字符，最多60字符。文本内容规则请看注意事项")
    private String title;

    /** 商品封面图,多个图片,以逗号分隔 */
    @Excel(name = "商品封面图,多个图片,以逗号分隔")
    private String headImgs;

    /** 视频小店商品id */
    @Excel(name = "视频小店商品id")
    private String productId;

    /** 问到系统关联的课程id,有可能是好课的,也可能是课堂的 */
    @Excel(name = "问到系统关联的课程id,有可能是好课的,也可能是课堂的")
    private Long wendaoCourseId;

    /** 问到抖音的appid,问到好课或问到课堂 */
    @Excel(name = "问到抖音的appid,问到好课或问到课堂")
    private String wendaoAppId;

    /** 老师id,问到课堂或者问到好课的 */
    @Excel(name = "老师id,问到课堂或者问到好课的")
    private Long teacherId;

    /** 上线的商品详情,json */
    @Excel(name = "上线的商品详情,json")
    private String productInfo;

    /** 商品详情草稿json */
    @Excel(name = "商品详情草稿json")
    private String editProductInfo;

    /** 推广方式,3专属推广,1普通推广 */
    @Excel(name = "推广方式,3专属推广,1普通推广")
    private Integer wxUnionStatus;

    /** 普通推广或专属推广的推广比例,百分号前面的数字. */
    @Excel(name = "普通推广或专属推广的推广比例,百分号前面的数字.")
    private Integer wxUnionRatio;

    /** 分享员推广佣金率,百分号前面的数字. */
    @Excel(name = "分享员推广佣金率,百分号前面的数字.")
    private Integer wxSharerRatio;

    /** 销量 */
    @Excel(name = "销量")
    private Integer saleNum;

    /** 视频号小店一级分类id */
    @Excel(name = "视频号小店一级分类id")
    private Integer wxFirstCatId;

    /** 视频号小店一级分类名称 */
    @Excel(name = "视频号小店一级分类名称")
    private String wxFirstCatName;

    /** 视频号小店二级分类id */
    @Excel(name = "视频号小店二级分类id")
    private Integer wxSecondCatId;

    /** 视频号小店二级分类名称 */
    @Excel(name = "视频号小店二级分类名称")
    private String wxSecondCatName;

    /** 视频号小店三级分类id */
    @Excel(name = "视频号小店三级分类id")
    private Integer wxThirdCatId;

    /** 视频号小店三级分类名称 */
    @Excel(name = "视频号小店三级分类名称")
    private String wxThirdCatName;

    @Excel(name = "微信小店appId")
    private String wxxdAppId;

    public String getWxxdAppId() {
        return wxxdAppId;
    }

    public void setWxxdAppId(String wxxdAppId) {
        this.wxxdAppId = wxxdAppId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setWxAuditStatus(Integer wxAuditStatus) 
    {
        this.wxAuditStatus = wxAuditStatus;
    }

    public Integer getWxAuditStatus() 
    {
        return wxAuditStatus;
    }
    public void setWxAuditMsg(String wxAuditMsg) 
    {
        this.wxAuditMsg = wxAuditMsg;
    }

    public String getWxAuditMsg() 
    {
        return wxAuditMsg;
    }
    public void setWxSaleStatus(Integer wxSaleStatus) 
    {
        this.wxSaleStatus = wxSaleStatus;
    }

    public Integer getWxSaleStatus() 
    {
        return wxSaleStatus;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setHeadImgs(String headImgs) 
    {
        this.headImgs = headImgs;
    }

    public String getHeadImgs() 
    {
        return headImgs;
    }
    public void setProductId(String productId) 
    {
        this.productId = productId;
    }

    public String getProductId() 
    {
        return productId;
    }
    public void setWendaoCourseId(Long wendaoCourseId) 
    {
        this.wendaoCourseId = wendaoCourseId;
    }

    public Long getWendaoCourseId() 
    {
        return wendaoCourseId;
    }
    public void setWendaoAppId(String wendaoAppId) 
    {
        this.wendaoAppId = wendaoAppId;
    }

    public String getWendaoAppId() 
    {
        return wendaoAppId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setProductInfo(String productInfo) 
    {
        this.productInfo = productInfo;
    }

    public String getProductInfo() 
    {
        return productInfo;
    }
    public void setEditProductInfo(String editProductInfo) 
    {
        this.editProductInfo = editProductInfo;
    }

    public String getEditProductInfo() 
    {
        return editProductInfo;
    }
    public void setWxUnionStatus(Integer wxUnionStatus) 
    {
        this.wxUnionStatus = wxUnionStatus;
    }

    public Integer getWxUnionStatus() 
    {
        return wxUnionStatus;
    }
    public void setWxUnionRatio(Integer wxUnionRatio) 
    {
        this.wxUnionRatio = wxUnionRatio;
    }

    public Integer getWxUnionRatio() 
    {
        return wxUnionRatio;
    }
    public void setWxSharerRatio(Integer wxSharerRatio) 
    {
        this.wxSharerRatio = wxSharerRatio;
    }

    public Integer getWxSharerRatio() 
    {
        return wxSharerRatio;
    }
    public void setSaleNum(Integer saleNum) 
    {
        this.saleNum = saleNum;
    }

    public Integer getSaleNum() 
    {
        return saleNum;
    }
    public void setWxFirstCatId(Integer wxFirstCatId) 
    {
        this.wxFirstCatId = wxFirstCatId;
    }

    public Integer getWxFirstCatId() 
    {
        return wxFirstCatId;
    }
    public void setWxFirstCatName(String wxFirstCatName) 
    {
        this.wxFirstCatName = wxFirstCatName;
    }

    public String getWxFirstCatName() 
    {
        return wxFirstCatName;
    }
    public void setWxSecondCatId(Integer wxSecondCatId) 
    {
        this.wxSecondCatId = wxSecondCatId;
    }

    public Integer getWxSecondCatId() 
    {
        return wxSecondCatId;
    }
    public void setWxSecondCatName(String wxSecondCatName) 
    {
        this.wxSecondCatName = wxSecondCatName;
    }

    public String getWxSecondCatName() 
    {
        return wxSecondCatName;
    }
    public void setWxThirdCatId(Integer wxThirdCatId) 
    {
        this.wxThirdCatId = wxThirdCatId;
    }

    public Integer getWxThirdCatId() 
    {
        return wxThirdCatId;
    }
    public void setWxThirdCatName(String wxThirdCatName) 
    {
        this.wxThirdCatName = wxThirdCatName;
    }

    public String getWxThirdCatName() 
    {
        return wxThirdCatName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("wxAuditStatus", getWxAuditStatus())
            .append("wxAuditMsg", getWxAuditMsg())
            .append("wxSaleStatus", getWxSaleStatus())
            .append("title", getTitle())
            .append("headImgs", getHeadImgs())
            .append("productId", getProductId())
            .append("wendaoCourseId", getWendaoCourseId())
            .append("wendaoAppId", getWendaoAppId())
            .append("teacherId", getTeacherId())
            .append("productInfo", getProductInfo())
            .append("editProductInfo", getEditProductInfo())
            .append("wxUnionStatus", getWxUnionStatus())
            .append("wxUnionRatio", getWxUnionRatio())
            .append("wxSharerRatio", getWxSharerRatio())
            .append("saleNum", getSaleNum())
            .append("wxFirstCatId", getWxFirstCatId())
            .append("wxFirstCatName", getWxFirstCatName())
            .append("wxSecondCatId", getWxSecondCatId())
            .append("wxSecondCatName", getWxSecondCatName())
            .append("wxThirdCatId", getWxThirdCatId())
            .append("wxThirdCatName", getWxThirdCatName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
