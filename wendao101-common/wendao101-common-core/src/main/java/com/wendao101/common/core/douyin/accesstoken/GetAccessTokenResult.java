package com.wendao101.common.core.douyin.accesstoken;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wendao101.common.core.douyin.accesstoken.child.AccessTokenData;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAccessTokenResult {
    // 错误码
    @JsonProperty("err_no")
    private Long errNo;

    // 错误信息
    @JsonProperty("err_tips")
    private String errTips;

    // 获取的 access_token 数据
    @JsonProperty("data")
    private AccessTokenData data;


}

