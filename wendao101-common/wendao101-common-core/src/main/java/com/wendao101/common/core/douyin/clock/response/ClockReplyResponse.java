package com.wendao101.common.core.douyin.clock.response;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ClockReplyResponse {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 打卡记录表id
     */
    @Excel(name = "打卡记录表id")
    private Long clockRecordId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    /**
     * 回复人id
     */
    @Excel(name = "回复人id")
    private Long replyId;

    /**
     * 回复人名称
     */
    @Excel(name = "回复人名称")
    private String replyName;

    /**
     * 回复人头像
     */
    @Excel(name = "回复人头像")
    private String replyImg;

    /**
     * 是否隐藏评论 0否 1是
     */
    @Excel(name = "是否隐藏评论 0否 1是")
    private Integer hideContentType;

    /**
     * 父id
     */
    @Excel(name = "父id")
    private Long parentId;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @Excel(name = "修改时间")
    private Date updateTime;
}
