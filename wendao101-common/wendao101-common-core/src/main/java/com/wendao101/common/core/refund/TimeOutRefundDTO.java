package com.wendao101.common.core.refund;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

@Data
public class TimeOutRefundDTO {
    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 退款状态,0待处理，1已经处理 */
    @Excel(name = "退款状态,0待处理，1已经处理")
    private Integer refundStatus;

    /** 退款类型,1已退款，2超时自动退款，3已拒绝退款 */
    @Excel(name = "退款状态,1已退款，2超时自动退款，3已拒绝退款")
    private Integer refundType;

    private String systemKey = "refund";
}
