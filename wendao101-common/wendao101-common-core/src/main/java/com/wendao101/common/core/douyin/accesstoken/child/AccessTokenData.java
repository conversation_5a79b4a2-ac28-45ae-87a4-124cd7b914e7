package com.wendao101.common.core.douyin.accesstoken.child;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessTokenData {

    // 获取的 access_token
    @JsonProperty("access_token")
    private String accessToken;

    // access_token 有效时间，单位：秒
    @JsonProperty("expires_in")
    private Long expiresIn;
}
