package com.wendao101.common.core.douyin.login.child;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Code2SessionResultData {
    // 会话密钥，如果请求时有 code 参数才会返回
    @JsonProperty("session_key")
    private String sessionKey;

    // 用户在当前小程序的 ID，如果请求时有 code 参数才会返回
    @JsonProperty("openid")
    private String openid;

    // 匿名用户在当前小程序的 ID，如果请求时有 anonymous_code 参数才会返回
    @JsonProperty("anonymous_openid")
    private String anonymousOpenid;

    // 用户在小程序平台的唯一标识符，请求时有 code 参数才会返回。如果开发者拥有多个小程序，可通过 unionid 来区分用户的唯一性。
    @JsonProperty("unionid")
    private String unionid;
}
