package com.wendao101.common.core.ktdto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * qualifications对象 qualifications
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
public class Qualifications
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** empid */
    private Integer empid;

    /** type */
    private Integer type;

    /** zzid */
    private String zzid;

    /** Name */
    @JsonProperty("Name")
    private String name;

    /** CardNo */
    @JsonProperty("CardNo")
    private String cardno;

    /** sfzyxq */
    private String sfzyxq;

    /** cardFront */
    @JsonProperty("cardFront")
    private String cardfront;

    /** cardBack */
    @JsonProperty("cardBack")
    private String cardback;

    /** headPicture */
    @JsonProperty("headPicture")
    private String headpicture;

    /** frontUri */
    @JsonProperty("frontUri")
    private String fronturi;

    /** backUri */
    @JsonProperty("backUri")
    private String backuri;

    /** headUri */
    @JsonProperty("headUri")
    private String headuri;

    /** nickName */
    @JsonProperty("nickName")
    private String nickname;

    /** introduce */
    @JsonProperty("introduce")
    private String introduce;

    /** TEFL */
    @JsonProperty("TEFL")
    private String tefl;

    /** TEFLuri */
    @JsonProperty("TEFLuri")
    private String tefluri;

    /** TEFLDate */
    @JsonProperty("TEFLDate")
    private String tefldate;

    /** TEFLNo */
    @JsonProperty("TEFLNo")
    private String teflno;

    /** Pc */
    @JsonProperty("Pc")
    private String pc;

    /** PcUri */
    @JsonProperty("PcUri")
    private String pcuri;

    /** PcDate */
    @JsonProperty("PcDate")
    private String pcdate;

    /** PcNo */
    @JsonProperty("PcNo")
    private String pcno;

    /** PAC */
    @JsonProperty("PAC")
    private String pac;

    /** PACuri */
    @JsonProperty("PACuri")
    private String pacuri;

    /** PACDate */
    @JsonProperty("PACDate")
    private String pacdate;

    /** PACNo */
    @JsonProperty("PACNo")
    private String pacno;

    /** companyName */
    @JsonProperty("companyName")
    private String companyname;

    /** IFN */
    @JsonProperty("IFN")
    private String ifn;

    /** osType */
    @JsonProperty("osType")
    private Integer ostype;

    /** obType */
    @JsonProperty("obType")
    private Integer obtype;

    /** logoImg */
    @JsonProperty("logoImg")
    private String logoimg;

    /** logouri */
    @JsonProperty("logouri")
    private String logouri;

    /** TRCImg */
    @JsonProperty("TRCImg")
    private String trcimg;

    /** TRCuri */
    @JsonProperty("TRCuri")
    private String trcuri;

    /** IBLImg */
    @JsonProperty("IBLImg")
    private String iblimg;

    /** Ibluri */
    @JsonProperty("Ibluri")
    private String ibluri;

    /** tovType */
    @JsonProperty("tovType")
    private String tovtype;

    /** begintime */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("begintime")
    private Date begintime;

    /** endtime */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("endtime")
    private Date endtime;

    /** BINo */
    @JsonProperty("BINo")
    private String bino;

    /** firstClass */
    @JsonProperty("firstClass")
    private Integer firstclass;

    /** secondClass */
    @JsonProperty("secondClass")
    private Integer secondclass;

    /** frname */
    @JsonProperty("frname")
    private String frname;

    /** status */
    @JsonProperty("status")
    private Integer status;

    /** reason */
    @JsonProperty("reason")
    private String reason;

    /** objectid */
    @JsonProperty("objectid")
    private String objectid;

    /** firstClassStr */
    @JsonProperty("firstClassStr")
    private String firstclassstr;

    /** secondClassStr */
    @JsonProperty("secondClassStr")
    private String secondclassstr;

    /** tskclm */
    @JsonProperty("tskclm")
    private String tskclm;

    /** tskclmuri */
    @JsonProperty("tskclmuri")
    private String tskclmuri;

    /** hzsm */
    @JsonProperty("hzsm")
    private String hzsm;

    /** hzsmuri */
    @JsonProperty("hzsmuri")
    private String hzsmuri;

    /** sqh */
    @JsonProperty("sqh")
    private String sqh;

    /** sqhuri */
    @JsonProperty("sqhuri")
    private String sqhuri;

    /** tskclmrz */
    @JsonProperty("tskclmrz")
    private String tskclmrz;

    /** tskclmrzuri */
    @JsonProperty("tskclmrzuri")
    private String tskclmrzuri;

    /** tskclmDate */
    @JsonProperty("tskclmDate")
    private String tskclmdate;

    /** tskclmrzDate */
    @JsonProperty("tskclmrzDate")
    private String tskclmrzdate;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setEmpid(Integer empid) 
    {
        this.empid = empid;
    }

    public Integer getEmpid() 
    {
        return empid;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setZzid(String zzid) 
    {
        this.zzid = zzid;
    }

    public String getZzid() 
    {
        return zzid;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setCardno(String cardno) 
    {
        this.cardno = cardno;
    }

    public String getCardno() 
    {
        return cardno;
    }
    public void setSfzyxq(String sfzyxq) 
    {
        this.sfzyxq = sfzyxq;
    }

    public String getSfzyxq() 
    {
        return sfzyxq;
    }
    public void setCardfront(String cardfront) 
    {
        this.cardfront = cardfront;
    }

    public String getCardfront() 
    {
        return cardfront;
    }
    public void setCardback(String cardback) 
    {
        this.cardback = cardback;
    }

    public String getCardback() 
    {
        return cardback;
    }
    public void setHeadpicture(String headpicture) 
    {
        this.headpicture = headpicture;
    }

    public String getHeadpicture() 
    {
        return headpicture;
    }
    public void setFronturi(String fronturi) 
    {
        this.fronturi = fronturi;
    }

    public String getFronturi() 
    {
        return fronturi;
    }
    public void setBackuri(String backuri) 
    {
        this.backuri = backuri;
    }

    public String getBackuri() 
    {
        return backuri;
    }
    public void setHeaduri(String headuri) 
    {
        this.headuri = headuri;
    }

    public String getHeaduri() 
    {
        return headuri;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setIntroduce(String introduce) 
    {
        this.introduce = introduce;
    }

    public String getIntroduce() 
    {
        return introduce;
    }
    public void setTefl(String tefl) 
    {
        this.tefl = tefl;
    }

    public String getTefl() 
    {
        return tefl;
    }
    public void setTefluri(String tefluri) 
    {
        this.tefluri = tefluri;
    }

    public String getTefluri() 
    {
        return tefluri;
    }
    public void setTefldate(String tefldate) 
    {
        this.tefldate = tefldate;
    }

    public String getTefldate() 
    {
        return tefldate;
    }
    public void setTeflno(String teflno) 
    {
        this.teflno = teflno;
    }

    public String getTeflno() 
    {
        return teflno;
    }
    public void setPc(String pc) 
    {
        this.pc = pc;
    }

    public String getPc() 
    {
        return pc;
    }
    public void setPcuri(String pcuri) 
    {
        this.pcuri = pcuri;
    }

    public String getPcuri() 
    {
        return pcuri;
    }
    public void setPcdate(String pcdate) 
    {
        this.pcdate = pcdate;
    }

    public String getPcdate() 
    {
        return pcdate;
    }
    public void setPcno(String pcno) 
    {
        this.pcno = pcno;
    }

    public String getPcno() 
    {
        return pcno;
    }
    public void setPac(String pac) 
    {
        this.pac = pac;
    }

    public String getPac() 
    {
        return pac;
    }
    public void setPacuri(String pacuri) 
    {
        this.pacuri = pacuri;
    }

    public String getPacuri() 
    {
        return pacuri;
    }
    public void setPacdate(String pacdate) 
    {
        this.pacdate = pacdate;
    }

    public String getPacdate() 
    {
        return pacdate;
    }
    public void setPacno(String pacno) 
    {
        this.pacno = pacno;
    }

    public String getPacno() 
    {
        return pacno;
    }
    public void setCompanyname(String companyname) 
    {
        this.companyname = companyname;
    }

    public String getCompanyname() 
    {
        return companyname;
    }
    public void setIfn(String ifn) 
    {
        this.ifn = ifn;
    }

    public String getIfn() 
    {
        return ifn;
    }
    public void setOstype(Integer ostype) 
    {
        this.ostype = ostype;
    }

    public Integer getOstype() 
    {
        return ostype;
    }
    public void setObtype(Integer obtype) 
    {
        this.obtype = obtype;
    }

    public Integer getObtype() 
    {
        return obtype;
    }
    public void setLogoimg(String logoimg) 
    {
        this.logoimg = logoimg;
    }

    public String getLogoimg() 
    {
        return logoimg;
    }
    public void setLogouri(String logouri) 
    {
        this.logouri = logouri;
    }

    public String getLogouri() 
    {
        return logouri;
    }
    public void setTrcimg(String trcimg) 
    {
        this.trcimg = trcimg;
    }

    public String getTrcimg() 
    {
        return trcimg;
    }
    public void setTrcuri(String trcuri) 
    {
        this.trcuri = trcuri;
    }

    public String getTrcuri() 
    {
        return trcuri;
    }
    public void setIblimg(String iblimg) 
    {
        this.iblimg = iblimg;
    }

    public String getIblimg() 
    {
        return iblimg;
    }
    public void setIbluri(String ibluri) 
    {
        this.ibluri = ibluri;
    }

    public String getIbluri() 
    {
        return ibluri;
    }
    public void setTovtype(String tovtype) 
    {
        this.tovtype = tovtype;
    }

    public String getTovtype() 
    {
        return tovtype;
    }
    public void setBegintime(Date begintime) 
    {
        this.begintime = begintime;
    }

    public Date getBegintime() 
    {
        return begintime;
    }
    public void setEndtime(Date endtime) 
    {
        this.endtime = endtime;
    }

    public Date getEndtime() 
    {
        return endtime;
    }
    public void setBino(String bino) 
    {
        this.bino = bino;
    }

    public String getBino() 
    {
        return bino;
    }
    public void setFirstclass(Integer firstclass) 
    {
        this.firstclass = firstclass;
    }

    public Integer getFirstclass() 
    {
        return firstclass;
    }
    public void setSecondclass(Integer secondclass) 
    {
        this.secondclass = secondclass;
    }

    public Integer getSecondclass() 
    {
        return secondclass;
    }
    public void setFrname(String frname) 
    {
        this.frname = frname;
    }

    public String getFrname() 
    {
        return frname;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setObjectid(String objectid) 
    {
        this.objectid = objectid;
    }

    public String getObjectid() 
    {
        return objectid;
    }
    public void setFirstclassstr(String firstclassstr) 
    {
        this.firstclassstr = firstclassstr;
    }

    public String getFirstclassstr() 
    {
        return firstclassstr;
    }
    public void setSecondclassstr(String secondclassstr) 
    {
        this.secondclassstr = secondclassstr;
    }

    public String getSecondclassstr() 
    {
        return secondclassstr;
    }
    public void setTskclm(String tskclm) 
    {
        this.tskclm = tskclm;
    }

    public String getTskclm() 
    {
        return tskclm;
    }
    public void setTskclmuri(String tskclmuri) 
    {
        this.tskclmuri = tskclmuri;
    }

    public String getTskclmuri() 
    {
        return tskclmuri;
    }
    public void setHzsm(String hzsm) 
    {
        this.hzsm = hzsm;
    }

    public String getHzsm() 
    {
        return hzsm;
    }
    public void setHzsmuri(String hzsmuri) 
    {
        this.hzsmuri = hzsmuri;
    }

    public String getHzsmuri() 
    {
        return hzsmuri;
    }
    public void setSqh(String sqh) 
    {
        this.sqh = sqh;
    }

    public String getSqh() 
    {
        return sqh;
    }
    public void setSqhuri(String sqhuri) 
    {
        this.sqhuri = sqhuri;
    }

    public String getSqhuri() 
    {
        return sqhuri;
    }
    public void setTskclmrz(String tskclmrz) 
    {
        this.tskclmrz = tskclmrz;
    }

    public String getTskclmrz() 
    {
        return tskclmrz;
    }
    public void setTskclmrzuri(String tskclmrzuri) 
    {
        this.tskclmrzuri = tskclmrzuri;
    }

    public String getTskclmrzuri() 
    {
        return tskclmrzuri;
    }
    public void setTskclmdate(String tskclmdate) 
    {
        this.tskclmdate = tskclmdate;
    }

    public String getTskclmdate() 
    {
        return tskclmdate;
    }
    public void setTskclmrzdate(String tskclmrzdate) 
    {
        this.tskclmrzdate = tskclmrzdate;
    }

    public String getTskclmrzdate() 
    {
        return tskclmrzdate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("empid", getEmpid())
            .append("type", getType())
            .append("zzid", getZzid())
            .append("name", getName())
            .append("cardno", getCardno())
            .append("sfzyxq", getSfzyxq())
            .append("cardfront", getCardfront())
            .append("cardback", getCardback())
            .append("headpicture", getHeadpicture())
            .append("fronturi", getFronturi())
            .append("backuri", getBackuri())
            .append("headuri", getHeaduri())
            .append("nickname", getNickname())
            .append("introduce", getIntroduce())
            .append("tefl", getTefl())
            .append("tefluri", getTefluri())
            .append("tefldate", getTefldate())
            .append("teflno", getTeflno())
            .append("pc", getPc())
            .append("pcuri", getPcuri())
            .append("pcdate", getPcdate())
            .append("pcno", getPcno())
            .append("pac", getPac())
            .append("pacuri", getPacuri())
            .append("pacdate", getPacdate())
            .append("pacno", getPacno())
            .append("companyname", getCompanyname())
            .append("ifn", getIfn())
            .append("ostype", getOstype())
            .append("obtype", getObtype())
            .append("logoimg", getLogoimg())
            .append("logouri", getLogouri())
            .append("trcimg", getTrcimg())
            .append("trcuri", getTrcuri())
            .append("iblimg", getIblimg())
            .append("ibluri", getIbluri())
            .append("tovtype", getTovtype())
            .append("begintime", getBegintime())
            .append("endtime", getEndtime())
            .append("bino", getBino())
            .append("firstclass", getFirstclass())
            .append("secondclass", getSecondclass())
            .append("frname", getFrname())
            .append("status", getStatus())
            .append("reason", getReason())
            .append("objectid", getObjectid())
            .append("firstclassstr", getFirstclassstr())
            .append("secondclassstr", getSecondclassstr())
            .append("tskclm", getTskclm())
            .append("tskclmuri", getTskclmuri())
            .append("hzsm", getHzsm())
            .append("hzsmuri", getHzsmuri())
            .append("sqh", getSqh())
            .append("sqhuri", getSqhuri())
            .append("tskclmrz", getTskclmrz())
            .append("tskclmrzuri", getTskclmrzuri())
            .append("tskclmdate", getTskclmdate())
            .append("tskclmrzdate", getTskclmrzdate())
            .toString();
    }
}
