package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 * "ActionStatus": "OK",
 * "ErrorCode": 0,
 * "ErrorInfo": ""
 * }
 */
@Data
public class ResultDTO {
    /**
     * 请求处理的结果：
     * OK 表示处理成功
     * FAIL 表示失败
     */
    @JsonProperty("ActionStatus")
    @JSONField(name = "ActionStatus")
    String actionStatus;
    /**
     * 错误码：
     * 0：表示成功
     * 非0：表示失败
     */
    @JsonProperty("ErrorCode")
    @JSONField(name = "ErrorCode")
    Integer errorCode;
    /**
     * 错误信息
     */
    @JsonProperty("ErrorInfo")
    @JSONField(name = "ErrorInfo")
    String errorInfo;

    public boolean isSuccess() {
        return this.errorCode == 0;
    }
}
