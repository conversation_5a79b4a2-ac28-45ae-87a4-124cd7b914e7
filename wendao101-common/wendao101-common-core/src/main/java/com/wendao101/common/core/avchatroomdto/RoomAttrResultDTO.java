package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * {
 * "ActionStatus": "OK",
 * "ErrorInfo": "",
 * "ErrorCode": 0,
 * "GroupAttrAry": [
 * {
 * "key": "attr_key1",
 * "value": "attr_val1"
 * },
 * {
 * "key": "attr_key2",
 * "value": "attr_val2"
 * }
 * ]
 * }
 */
@Data
public class RoomAttrResultDTO {
    /**
     * 请求处理的结果：
     * OK 表示处理成功
     * FAIL 表示失败
     */
    @JsonProperty("ActionStatus")
    @JSONField(name = "ActionStatus")
    private String actionStatus;

    /**
     * 错误信息
     */
    @JsonProperty("ErrorInfo")
    @JSONField(name = "ErrorInfo")
    private String errorInfo;

    /**
     * 错误码：
     * 0表示成功
     * 非0表示失败
     */
    @JsonProperty("ErrorCode")
    @JSONField(name = "ErrorCode")
    private Integer errorCode;

    /**
     * 属性列表
     */
    @JsonProperty("GroupAttrAry")
    @JSONField(name = "GroupAttrAry")
    private List<GroupAttrDTO> groupAttrAry;

}
