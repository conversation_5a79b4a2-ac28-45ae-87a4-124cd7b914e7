package com.wendao101.common.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 资质中心信息对象 public_wendao_cert_info
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Data
public class PublicWendaoCertInfoMessage
{
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 实体类型 1: 个人 2:企业 */
    @Excel(name = "实体类型 1: 个人 2:企业")
    private Long entityType;

    /** 个人-身份证正面链接,人像面 */
    @Excel(name = "个人-身份证正面链接,人像面")
    private String frontPath;

    /** 个人-身份反面链接,国徽面 */
    @Excel(name = "个人-身份反面链接,国徽面")
    private String backPath;

    /** 个人-身份证号码 */
    @Excel(name = "个人-身份证号码")
    private String idNumber;

    /** 个人-老师真实姓名 */
    @Excel(name = "个人-老师真实姓名")
    private String teacherRealName;

    /** 个人-身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "个人-身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String idCardExpireTime;

    /** 个人和机构-头像 */
    @Excel(name = "个人和机构-头像")
    private String avatarPath;

    /** 个人和机构-昵称 */
    @Excel(name = "个人和机构-昵称")
    private String nickName;

    /** 个人和机构-老师介绍、200字以内 */
    @Excel(name = "个人和机构-老师介绍、200字以内")
    private String teacherDesc;

    /** 机构-营业执照编号或者统一社会信用代码 */
    @Excel(name = "机构-营业执照编号或者统一社会信用代码")
    private String businessLicenseNo;

    /** 机构-营业执照过期时间，示例：2029-09-10 */
    @Excel(name = "机构-营业执照过期时间，示例：2029-09-10")
    private String businessLicenseExpireTime;

    /** 机构-营业执照上的公司名称 */
    @Excel(name = "机构-营业执照上的公司名称")
    private String businessLicenseCompanyName;

    /** 机构-营业执照文件访问路径 */
    @Excel(name = "机构-营业执照文件访问路径")
    private String businessLicensePath;

    /** 机构-法人代表身份证正面链接,人像面 */
    @Excel(name = "机构-法人代表身份证正面链接,人像面")
    private String legalPersonFrontPath;

    /** 机构-法人代表身份反面链接,国徽面 */
    @Excel(name = "机构-法人代表身份反面链接,国徽面")
    private String legalPersonBackPath;

    /** 机构-法人代表姓名 */
    @Excel(name = "机构-法人代表姓名")
    private String legalPersonName;

    /** 机构-法人代表身份证号码 */
    @Excel(name = "机构-法人代表身份证号码")
    private String legalPersonIdNumber;

    /** 机构-法人代表身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构-法人代表身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String legalPersonIdCardExpireTime;

    /** 机构-机构场景类型（仅可填写"线上机构"或"线下机构"） */
    @Excel(name = "机构-机构场景类型")
    private String institutionSceneType;

    /** 机构-机构主体类型（仅可填"企业工商户"或"个体工商户"） */
    @Excel(name = "机构-机构主体类型")
    private String institutionSubjectType;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 证书类型，2:教师资格证,3:专业性学历证书,4:专业认证 */
    @Excel(name = "证书类型，2:教师资格证,3:专业性学历证书,4:专业认证")
    private Long certificateType;

    /** 证书图片路径链接 */
    @Excel(name = "证书图片路径链接")
    private String certificatePath;

    /** 证书有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "证书有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String certificateExpireTime;

    /** 授权函图片路径链接 */
    @Excel(name = "授权函图片路径链接")
    private String letterOfAuthorizationPath;

    /** 授权函有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "授权函有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String letterExpireTime;

    /** 机构类目-老师身份证正面链接,人像面 */
    @Excel(name = "机构类目-老师身份证正面链接,人像面")
    private String classTeacherFrontPath;

    /** 机构类目-老师身份反面链接,国徽面 */
    @Excel(name = "机构类目-老师身份反面链接,国徽面")
    private String classTeacherBackPath;

    /** 机构类目-老师姓名 */
    @Excel(name = "机构类目-老师姓名")
    private String classTeacherName;

    /** 机构类目-老师身份证号码 */
    @Excel(name = "机构类目-老师身份证号码")
    private String classTeacherIdNumber;

    /** 机构类目-老师身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构类目-老师身份证有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String classTeacherIdCardExpireTime;

    /** 机构类目-合作声明图片路径链接 */
    @Excel(name = "机构类目-合作声明图片路径链接")
    private String cooperationStatementPath;

    /** 机构类目-合作声明有效期 示例：2034-01-09，长期有效可传：2099-12-31 */
    @Excel(name = "机构类目-合作声明有效期 示例：2034-01-09，长期有效可传：2099-12-31")
    private String cooperationStatementExpireTime;

    /** 审核状态 0审核中，1审核通过，-1审核拒绝 */
    @Excel(name = "审核状态 0审核中，1审核通过，-1审核拒绝")
    private Integer auditStatus;

    /** 定时任务状态，0未开始，1已推送，2已回调 */
    @Excel(name = "定时任务状态，0未开始，1已推送，2已回调")
    private Integer jobStatus;

    /** 抖音审核拒绝消息 */
    @Excel(name = "抖音审核拒绝消息")
    private String douyinAuditMessage;

    /** 提交审核后抖音基本信息审核id */
    @Excel(name = "提交审核后抖音基本信息审核id")
    private String basicAuthTaskid;

    /** 提交审核后抖音类目信息审核id */
    @Excel(name = "提交审核后抖音类目信息审核id")
    private String classAuthTaskid;

    /** 提交审核后抖音生成的实体id */
    @Excel(name = "提交审核后抖音生成的实体id")
    private String entityId;

    /** 抖音提交审核错误信息 */
    @Excel(name = "抖音提交审核错误信息")
    private String errMsg;

    /** 抖音提交审核错误code */
    @Excel(name = "抖音提交审核错误code")
    private Long errCode;

    /** 0未授权,1已授权 */
    @Excel(name = "0未授权,1已授权")
    private Integer authRoleStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
