package com.wendao101.common.core.doudian;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class AfterSaleReasons {
    private static final Map<Long, String> reasonMap = new HashMap<Long, String>() {{
        put(1L, "多拍/错拍/不想要");
        put(2L, "未按约定时间发货");
        put(3L, "快递一直未送达");
        put(4L, "快递无跟踪记录");
        put(5L, "收到商品少件/错件/空包裹");
        put(6L, "不喜欢/效果不好");
        put(7L, "商品做工粗糙/有瑕疵");
        put(8L, "商品功能有问题");
        put(9L, "效果与商品描述不符");
        put(10L, "商品材质/品牌/外观等与商品描述不符");
        put(11L, "生产日期/保质期/规格等与商品描述不符");
        put(12L, "假冒品牌");
        put(13L, "协商一致退款");
        put(14L, "缺货");
        put(15L, "其他");
        put(16L, "大小／尺寸／重量与商品描述不符");
        put(17L, "生产日期／保质期与商品描述不符");
        put(18L, "品种／规格／成分等与商品描述不符");
        put(19L, "商品腐烂/变质");
        put(20L, "少件／漏发");
        put(21L, "包装/商品破损");
        put(22L, "商家发错货");
        put(23L, "与商家协商一致退款");
        put(24L, "退运费");
        put(25L, "品种／产品／规格／成分等与商品描述不符");
        put(26L, "商品开箱腐烂／变质／死亡／残断");
        put(27L, "商品变质／过期");
        put(28L, "规格等与商品描述不符");
        put(29L, "收到商品少件／错件／空包裹");
        put(30L, "盗版");
        put(31L, "做工粗糙/有瑕疵/有污渍");
        put(32L, "商品损坏/损坏");
        put(33L, "商品材质/外观等与商品描述不符");
        put(34L, "使用后过敏");
        put(35L, "商品重复销售/缺货");
        put(36L, "想更换其他商品");
        put(37L, "已在其他地方购买");
        put(38L, "商品成色与商品描述不符");
        put(39L, "商品瑕疵与商品描述不符");
        put(40L, "商品尺寸与商品描述不符");
        put(41L, "商品与订单信息不符");
        put(42L, "商品漏发配件/赠品");
        put(43L, "想购买其他商品");
        put(44L, "商品材质/瑕疵/尺寸等与商品描述不符");
        put(45L, "商品做工粗糙/品质差");
        put(46L, "商品破损/包装破损");
        put(47L, "商品漏发配件/赠品/证书");
        put(48L, "未收到商品");
        put(49L, "预约不上");
        put(50L, "联系不到商家");
        put(51L, "无法核销");
        put(52L, "计划有变，无时间消费");
        put(53L, "商家要求用其他方式进行消费");
        put(54L, "兑换地点太远");
        put(55L, "线下更优惠");
        put(56L, "商家平台功能体验差");
        put(57L, "课程进度/难度不合适");
        put(58L, "主讲老师能力不匹配");
        put(59L, "不想继续上课");
        put(60L, "赠品未寄送");
        put(61L, "商家站外导流");
        put(62L, "商家评价不好");
        put(63L, "商家关店、装修、转让");
        put(64L, "联系不上商家，或实地地址无此店");
        put(65L, "店里活动更优惠");
        put(66L, "商家要求用其他方式进行消费");
        put(67L, "功能/效果与商品描述不符");
        put(68L, "宠物食用不适");
        put(69L, "商品尺寸/尺码/大小与实物不符");
        put(70L, "商品材质面料与实物描述不符");
        put(71L, "商品颜色/图案/款式与实物不符");
        put(72L, "外观/型号/规格/参数与商品描述不符");
        put(73L, "食用后不适");
        put(74L, "商家未按约定时间发货");
        put(75L, "系统签收商品未收到");
        put(76L, "快递信息长时间未更新");
        put(77L, "商品/赠品/配件/证书漏发");
        put(78L, "商家发错货");
        put(79L, "多拍/错拍/不想要");
        put(80L, "计划有变，无时间消费");
        put(81L, "有更优惠的购买方式");
        put(82L, "门店缺货/无法使用");
        put(83L, "7天无理由退款");
        put(84L, "多拍/错拍/不想要");
        put(85L, "计划有变，无时间学习");
        put(86L, "课程无法观看/app无法下载");
        put(87L, "以为购买的是实物商品");
        put(88L, "不知道如何观看课程");
        put(89L, "课程平台功能体验差");
        put(90L, "商家虚假宣传");
        put(91L, "商家售后服务差");
        put(92L, "商家引导用其他方式进行消费");
        put(93L, "信息填错");
        put(94L, "行程取消");
        put(95L, "有更优惠的购买方式");
        put(96L, "担心自然灾害/政治原因/疫情");
        put(97L, "签证拒签/航班终止");
        put(98L, "商品体验不好");
        put(99L, "充值未到账");
        put(100L, "充值少到账");
        put(101L, "商家评价不好");
        put(102L, "商家要求用其他方式进行消费");
        put(103L, "商品腐烂／变质／死亡／残断");
        put(104L, "商家爽约/不接待");
        put(105L, "地址/电话信息填写错误");
        put(106L, "未收到课程/资料/服务等");
        put(107L, "商品质量问题");
        put(108L, "有隐形消费项目");
        put(109L, "临近过期");
        put(110L, "商品信息拍错（规格/尺码/颜色等）");
        put(111L, "不想要了");
        put(112L, "商品买贵了或降价");
        put(113L, "拍多了");
        put(114L, "商品已下架");
        put(115L, "商品未按约定设置佣金比例");
        put(116L, "商品信息拍错（规格/品种/重量等）");
        put(117L, "商品信息拍错（规格/样式/颜色等）");
        put(118L, "商品信息拍错（规格/品种等）");
        put(119L, "商品信息拍错（规格/重量等）");
        put(120L, "商品信息拍错（型号/规格等）");
        put(121L, "商品信息拍错（种类/数量等）");
        put(122L, "包裹异常");
        put(124L, "标签/规格/包装等与商品描述不符");
        put(125L, "大小/颜色/型号不符");
        put(126L, "发票问题");
        put(127L, "活动/优惠券未兑现");
        put(128L, "卡券无法使用");
        put(129L, "开箱时有残断/变形/枯萎/死亡");
        put(130L, "快递/物流一直未送到");
        put(131L, "快递签收实物未收到");
        put(132L, "卖家少发商品");
        put(133L, "没用/少用/错用优惠");
        put(134L, "配件保养");
        put(136L, "商家发货慢");
        put(137L, "商品变质/发霉/有异物");
        put(138L, "商品口感差");
        put(139L, "商品评价不好");
        put(140L, "商品破损/腐烂");
        put(141L, "商品未成熟");
        put(142L, "商品已拒签（货物破损等）");
        put(143L, "商品质量问题（变质/发霉等）");
        put(144L, "商品质量问题（变质/死亡等）");
        put(145L, "商品质量问题（损坏/变形等）");
        put(146L, "商品质量问题（损坏/掉漆等）");
        put(147L, "商品质量问题（褪色/掉色/发黑等）");
        put(148L, "商品质量问题（异味/变质等）");
        put(149L, "商品质量问题（有异物/变质等）");
        put(150L, "预约不到/卖家不给兑换");
        put(151L, "赠品漏发/少发");
        put(152L, "质保期外问题");
        put(153L, "做工问题/工艺瑕疵/掉漆/褪色");
        put(154L, "商品与页面描述不符");
        put(155L, "褪色/掉漆/掉钻等");
        put(156L, "未送货上门");
        put(157L, "颜色/图案/款式与商品描述不符");
        put(158L, "已过保质期");
        put(159L, "商品信息拍错（规格/大小/颜色等）");
        put(160L, "成分/材质与商品描述不符");
        put(161L, "商品尺寸/尺码/大小与预期不符");
        put(162L, "我不想要了");
        put(163L, "缺重自动退款");
        put(164L, "配送太慢");
        put(165L, "骑手通知我无法配送");
        put(166L, "送错了/少送了");
        put(167L, "系统显示已送达商品未收到");
        put(168L, "忘记使用优惠券");
        put(169L, "商品化冻/融化");
        put(170L, "用户拒收");
        put(171L, "电子资料/服务数量不足");
        put(172L, "兑换问题（时效/方式等）");
        put(173L, "课程进度与页面描述不符");
        put(174L, "课程难度不合适");
        put(175L, "联系人信息填写错误");
        put(176L, "券码/充值未在承诺时间内到账");
        put(177L, "商品信息拍错（数量/金额等)");
        put(178L, "商品信息拍错（套餐/归属地等）");
        put(179L, "套餐/资费/期限/余额不符");
        put(180L, "验证失败/开卡失败");
        put(181L, "使用中质量问题");
        put(182L, "未按规定时间送达");
        put(183L, "无骑手接单");
        put(184L, "骑手沟通态度差");
        put(185L, "其他平台更便宜");
        put(186L, "服务商不提供安装服务");
        put(187L, "安装破损");
        put(188L, "送达时间选择有误");
        put(189L, "填错地址/不方便收货");
        put(190L, "买错款式或颜色");
        put(191L, "大小/尺寸不合适");
        put(192L, "大小/颜色/型号等不合适");
        put(193L, "宠物不爱吃");
        put(194L, "食用后身体不适");
        put(195L, "买错口味/规格等");
        put(196L, "成分造假/饮用后不适");
        put(197L, "宝宝不爱吃/口感不好");
        put(198L, "材质/成分造假");
        put(199L, "宠物状态不好/死亡");
        put(200L, "服务态度不好");
        put(201L, "课程无法观看/app无法下载");
        put(202L, "口感不好");
        put(203L, "买错版本/科目/年级等");
        put(204L, "买错规格/度数等");
        put(205L, "买错款式/克重等");
        put(206L, "买错品种/规格等");
        put(207L, "商品腐烂/死亡/残断");
        put(208L, "商品效果不好");
        put(209L, "商品信息拍错");
        put(210L, "填错联系人信息");
        put(211L, "未在承诺时间到账");
        put(212L, "未在承诺时间提供服务");
        put(213L, "一直没收到货");
        put(214L, "提供的服务不符");
        put(215L, "未收到卡号或卡密");
        put(216L, "发货超时/物流异常");
    }};

    public static String getReasonStr(Long reasonCode) {
        String s = reasonMap.get(reasonCode);
        if(StringUtils.isBlank(s)){
            return "未知售后原因!";
        }
        return s;
    }
}
