package com.wendao101.common.core.utils.qvideo;

import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;

/**
 * 腾讯云点播Key防盗链URL生成工具类
 *
 * <AUTHOR>
 */
public class KeyValidateUtils {

    /**
     * 默认KEY值，实际使用时应该从配置文件中读取
     */
    private static final String DEFAULT_KEY = "0JPU3MpOsC2tQjTOkZTQ";

    /**
     * 默认最大IP数
     */
    private static final int DEFAULT_MAX_IPS = 4;

    /**
     * 24小时的秒数
     */
    private static final long TWELVE_FOUR_HOURS_IN_SECONDS = 12 * 60 * 60;

    /**
     * 生成防盗链URL
     *
     * @param sourceUrl 原始完整视频URL
     * @return 生成的防盗链URL
     */
    public static String generateAntiLeechUrl(String sourceUrl) {
        return generateAntiLeechUrl(sourceUrl, null, null, null, null);
    }

    /**
     * 生成防盗链URL
     *
     * @param sourceUrl 原始完整视频URL
     * @return 生成的防盗链URL
     */
    public static String generateAntiLeechUrl(String sourceUrl,Long expireTime) {
        return generateAntiLeechUrl(sourceUrl, expireTime, null, null, null);
    }

    /**
     * 生成防盗链试看URL
     *
     * @param sourceUrl 原始完整视频URL
     * @param exper     试看时长，单位为秒，以十进制表示，不填或者填0表示不试看（即返回完整视频）。试看时长不要超过视频原始时长，否则可能导致播放失败。
     * @return 生成的防盗链URL
     */
    public static String generateAntiLeechTreeSeeUrl(String sourceUrl, Integer exper) {
        return generateAntiLeechUrl(sourceUrl, null, null, null, exper);
    }

    /**
     * 生成防盗链URL
     *
     * @param sourceUrl  原始完整视频URL
     * @param expireTime 过期时间(秒)，如果不传则默认为当前时间+24小时
     * @param us         请求标识
     * @param rlimit     最大IP数，不传则默认为4
     * @return 生成的防盗链URL
     */
    public static String generateAntiLeechUrl(String sourceUrl, Long expireTime, String us, Integer rlimit, Integer exper) {
        if (StringUtils.isBlank(sourceUrl)) {
            return sourceUrl;
        }

        // 如果没有传入过期时间，默认为当前时间+24小时
        if (expireTime == null) {
            expireTime = Instant.now().getEpochSecond() + TWELVE_FOUR_HOURS_IN_SECONDS;
        }

        // 如果没有传入最大IP数，使用默认值
        if (rlimit == null) {
            rlimit = DEFAULT_MAX_IPS;
        }

        // 如果没有传入请求标识，生成一个随机字符串
        if (StringUtils.isBlank(us)) {
            us = generateRandomString();
        }

        try {
            // 获取视频路径
            String dir = getDir(sourceUrl);

            // 将过期时间转换为16进制
            String hexTime = Long.toHexString(expireTime).toLowerCase();

            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            signStr.append(DEFAULT_KEY)
                    .append(dir)
                    .append(hexTime);
            if (exper != null) {
                signStr.append(exper);
            }
            signStr.append(rlimit)
                    .append(us);

            // 计算MD5签名
            String sign = md5(signStr.toString());

            // 构建防盗链URL
            StringBuilder urlBuilder = new StringBuilder(sourceUrl);
            urlBuilder.append("?t=").append(hexTime);
            if (exper != null) {
                urlBuilder.append("&exper=").append(exper);
            }
            urlBuilder.append("&rlimit=").append(rlimit)
                    .append("&us=").append(us)
                    .append("&sign=").append(sign);

            return urlBuilder.toString();
        } catch (Exception e) {
            throw new RuntimeException("Generate anti leech url failed", e);
        }
    }

    /**
     * 获取视频路径
     */
    private static String getDir(String sourceUrl) {
        String path = sourceUrl.substring(sourceUrl.indexOf("/", 8));
        return path.substring(0, path.lastIndexOf("/") + 1);
    }

    /**
     * 生成随机字符串作为us参数
     */
    private static String generateRandomString() {
        return Long.toHexString(System.nanoTime());
    }

    /**
     * 计算MD5值
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

//    public static void main(String[] args) {
//        long FORTY_EIGHT_HOURS_IN_SECONDS = 48 * 60 * 60;
//        String sourceUrl = "https://1319546384.vod-qcloud.com/667f701dvodtranssh1319546384/cd5ef4581397757907397315745/v.f100030.mp4";
//        long expireTime = Instant.now().getEpochSecond() + FORTY_EIGHT_HOURS_IN_SECONDS;
//        String antiLeechUrl = KeyValidateUtils.generateAntiLeechUrl(sourceUrl,expireTime);
//        System.out.println(antiLeechUrl);
//    }
}
