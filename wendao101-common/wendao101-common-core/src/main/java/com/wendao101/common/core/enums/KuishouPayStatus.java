package com.wendao101.common.core.enums;

/**
 * 订单支付状态。 取值： PROCESSING-处理中｜SUCCESS-成功｜FAILED-失败
 */
public enum KuishouPayStatus {
    PROCESSING("PROCESSING", "处理中"),

    SUCCESS("SUCCESS", "成功"),

    FAILED("FAILED", "失败");

    private final String code;
    private final String ksName;

    KuishouPayStatus(String code, String ksName) {
        this.code = code;
        this.ksName = ksName;
    }
    public String getCode() {
        return code;
    }

    public String getKsName() {
        return ksName;
    }

    public static KuishouPayStatus getEnum(String code) {
        for (KuishouPayStatus payWay : KuishouPayStatus.values()) {
            if (payWay.getCode().equals(code)) {
                return payWay;
            }
        }
        return KuishouPayStatus.PROCESSING;
    }
}
