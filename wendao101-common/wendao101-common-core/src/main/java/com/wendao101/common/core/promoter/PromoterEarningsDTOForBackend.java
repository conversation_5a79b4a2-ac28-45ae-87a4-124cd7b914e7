package com.wendao101.common.core.promoter;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PromoterEarningsDTOForBackend {

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 今天总计收益金额
     */
    @Excel(name = "今天总计收益金额")
    private BigDecimal todaySumPrice = BigDecimal.ZERO;

    /**
     * 昨天总计收益金额
     */
    @Excel(name = "昨天总计收益金额")
    private BigDecimal yesterdaySumPrice = BigDecimal.ZERO;

    /**
     * 在途金额
     */
    @Excel(name = "在途金额")
    private BigDecimal fundsTransitPrice = BigDecimal.ZERO;

    /**
     * 可提现金额
     */
    @Excel(name = "可提现金额")
    private BigDecimal mayWithdrawPrice = BigDecimal.ZERO;

    /**
     * 总计到账金额
     */
    @Excel(name = "总计到账金额")
    private BigDecimal sumAccountPrice = BigDecimal.ZERO;
}
