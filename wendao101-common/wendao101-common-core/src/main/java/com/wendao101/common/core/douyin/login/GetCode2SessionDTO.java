package com.wendao101.common.core.douyin.login;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCode2SessionDTO {

    // 小程序 ID
    @JsonProperty("appid")
    private String appid;

    // 小程序的 APP Secret，可以在开发者后台获取
    @JsonProperty("secret")
    private String secret;

    // login 接口返回的登录凭证
    @JsonProperty("code")
    private String code;

    // login 接口返回的匿名登录凭证
    @JsonProperty("anonymous_code")
    private String anonymousCode;

}
