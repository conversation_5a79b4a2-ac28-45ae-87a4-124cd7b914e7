package com.wendao101.common.core.douyin.accesstoken;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetClientTokenDTO {

    // 应用唯一标识，对应小程序id
    @JsonProperty("client_key")
    private String clientKey;

    // 应用唯一标识对应的密钥，对应小程序的app secret，可以在开发者后获取
    @JsonProperty("client_secret")
    private String clientSecret;

    // 固定值“client_credential”
    @JsonProperty("grant_type")
    private String grantType;

}

