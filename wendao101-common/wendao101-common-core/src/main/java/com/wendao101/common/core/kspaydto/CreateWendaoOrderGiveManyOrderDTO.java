package com.wendao101.common.core.kspaydto;

import lombok.Data;

import java.util.List;

@Data
public class CreateWendaoOrderGiveManyOrderDTO {
    /**
     * 用户的openId
     */
    private String openid;
    /**
     * 课程id
     */
    private long courseId;
    /**
     * 推广员id
     */
    private long promoterId;
    /**
     * 优惠券id
     */
    private long couponId;
    /**
     *下单平台,默认是快手 1微信 2快手
     */
    private Integer platform;
    /**
     *下单app名称,1问到好课,2问到课堂
     */
    private int appNameType=1;

    /**
     * 当appNameType=2时有效,默认问到课堂
     */
    private String appId="ks685954519696767360";

    //秒杀
    private Long secondKillId;
    private String secondKillUUID;

    //知识店铺新增
    //如果是h5的用户必须传userId;
    private Long userId;

    private List<Long> courseIds;

    private String primaryOrderId;
}
