package com.wendao101.common.core.web.page;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TableDataInfoWithEmployeeDataSum implements Serializable{

    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据 */
    private List<?> rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;




    /** 交易金额合计 */
    private BigDecimal dealAmount;

    /** 已提现金额合计 */
    private BigDecimal withdrawnAmount;

    /** 在途资金合计 */
    private BigDecimal moneyInTransit;

    /** 可提现金额合计 */
    private BigDecimal withdrawableAmount;

    /** 老师已提现金额收取的毛抽佣费合计*/
    private BigDecimal withdrawnAmountFee;
    /** 老师未提现金额将来要抽的毛抽佣费用合计 */
    private BigDecimal notWithdrawnFee;

    /**
     * 已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用  合计
     */
    private BigDecimal totalWithdrawnFee;


    /**
     * 已提现净抽佣  合计
     */
    private BigDecimal withdrawnNetCommission;
    /**
     * 未提现净抽佣 合计
     */
    private BigDecimal notWithdrawnNetCommission;

    /**
     * 已提现净抽佣+未提现净抽佣 合计
     */
    private BigDecimal totalWithdrawnNetCommission;


    /**
     * 个人佣金
     */
    private BigDecimal personCommission;
    /**
     * 团队部门佣金
     */
    private BigDecimal deptCommission;
    /**
     * 个人和团队合计佣金
     */
    private BigDecimal totalPersonDeptCommission;

    public BigDecimal getDealAmount() {
        return dealAmount;
    }

    public void setDealAmount(BigDecimal dealAmount) {
        this.dealAmount = dealAmount;
    }

    public BigDecimal getWithdrawnAmount() {
        return withdrawnAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) {
        this.withdrawnAmount = withdrawnAmount;
    }

    public BigDecimal getMoneyInTransit() {
        return moneyInTransit;
    }

    public void setMoneyInTransit(BigDecimal moneyInTransit) {
        this.moneyInTransit = moneyInTransit;
    }

    public BigDecimal getWithdrawableAmount() {
        return withdrawableAmount;
    }

    public void setWithdrawableAmount(BigDecimal withdrawableAmount) {
        this.withdrawableAmount = withdrawableAmount;
    }

    public BigDecimal getWithdrawnAmountFee() {
        return withdrawnAmountFee;
    }

    public void setWithdrawnAmountFee(BigDecimal withdrawnAmountFee) {
        this.withdrawnAmountFee = withdrawnAmountFee;
    }

    public BigDecimal getNotWithdrawnFee() {
        return notWithdrawnFee;
    }

    public void setNotWithdrawnFee(BigDecimal notWithdrawnFee) {
        this.notWithdrawnFee = notWithdrawnFee;
    }

    public BigDecimal getTotalWithdrawnFee() {
        return totalWithdrawnFee;
    }

    public void setTotalWithdrawnFee(BigDecimal totalWithdrawnFee) {
        this.totalWithdrawnFee = totalWithdrawnFee;
    }

    public BigDecimal getWithdrawnNetCommission() {
        return withdrawnNetCommission;
    }

    public void setWithdrawnNetCommission(BigDecimal withdrawnNetCommission) {
        this.withdrawnNetCommission = withdrawnNetCommission;
    }

    public BigDecimal getNotWithdrawnNetCommission() {
        return notWithdrawnNetCommission;
    }

    public void setNotWithdrawnNetCommission(BigDecimal notWithdrawnNetCommission) {
        this.notWithdrawnNetCommission = notWithdrawnNetCommission;
    }

    public BigDecimal getTotalWithdrawnNetCommission() {
        return totalWithdrawnNetCommission;
    }

    public void setTotalWithdrawnNetCommission(BigDecimal totalWithdrawnNetCommission) {
        this.totalWithdrawnNetCommission = totalWithdrawnNetCommission;
    }

    /**
     * 表格数据对象
     */
    public TableDataInfoWithEmployeeDataSum()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfoWithEmployeeDataSum(List<?> list, int total)
    {
        this.rows = list;
        this.total = total;
    }

    public static TableDataInfoWithEmployeeDataSum error(String msg){
        TableDataInfoWithEmployeeDataSum tableDataInfo = new TableDataInfoWithEmployeeDataSum();
        tableDataInfo.setCode(500);
        tableDataInfo.setMsg(msg);
        return tableDataInfo;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public List<?> getRows()
    {
        return rows;
    }

    public void setRows(List<?> rows)
    {
        this.rows = rows;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }
}
