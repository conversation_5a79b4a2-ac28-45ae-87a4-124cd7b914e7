package com.wendao101.common.core.web.page;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class TableDataInfoWithSumSaleMoney implements Serializable{

    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据 */
    private List<?> rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;


    //private int totalRecordCount;
    private BigDecimal totalDealAmount;
    //withdrawn_amount
    private BigDecimal totalWithdrawnAmount;
    //money_in_transit
    private BigDecimal totalMoneyInTransit;
    //withdrawable_amount
    private BigDecimal totalWithdrawableAmount;
    //service_fee
    private BigDecimal totalServiceFee;
    //service_fee_rate
    private BigDecimal totalServiceFeeRate;


    public BigDecimal getTotalDealAmount() {
        return totalDealAmount;
    }

    public void setTotalDealAmount(BigDecimal totalDealAmount) {
        this.totalDealAmount = totalDealAmount;
    }

    public BigDecimal getTotalWithdrawnAmount() {
        return totalWithdrawnAmount;
    }

    public void setTotalWithdrawnAmount(BigDecimal totalWithdrawnAmount) {
        this.totalWithdrawnAmount = totalWithdrawnAmount;
    }

    public BigDecimal getTotalMoneyInTransit() {
        return totalMoneyInTransit;
    }

    public void setTotalMoneyInTransit(BigDecimal totalMoneyInTransit) {
        this.totalMoneyInTransit = totalMoneyInTransit;
    }

    public BigDecimal getTotalWithdrawableAmount() {
        return totalWithdrawableAmount;
    }

    public void setTotalWithdrawableAmount(BigDecimal totalWithdrawableAmount) {
        this.totalWithdrawableAmount = totalWithdrawableAmount;
    }

    public BigDecimal getTotalServiceFee() {
        return totalServiceFee;
    }

    public void setTotalServiceFee(BigDecimal totalServiceFee) {
        this.totalServiceFee = totalServiceFee;
    }

    public BigDecimal getTotalServiceFeeRate() {
        return totalServiceFeeRate;
    }

    public void setTotalServiceFeeRate(BigDecimal totalServiceFeeRate) {
        this.totalServiceFeeRate = totalServiceFeeRate;
    }

    /**
     * 表格数据对象
     */
    public TableDataInfoWithSumSaleMoney()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfoWithSumSaleMoney(List<?> list, int total)
    {
        this.rows = list;
        this.total = total;
    }

    public static TableDataInfoWithSumSaleMoney error(String msg){
        TableDataInfoWithSumSaleMoney tableDataInfo = new TableDataInfoWithSumSaleMoney();
        tableDataInfo.setCode(500);
        tableDataInfo.setMsg(msg);
        return tableDataInfo;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public List<?> getRows()
    {
        return rows;
    }

    public void setRows(List<?> rows)
    {
        this.rows = rows;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }
}
