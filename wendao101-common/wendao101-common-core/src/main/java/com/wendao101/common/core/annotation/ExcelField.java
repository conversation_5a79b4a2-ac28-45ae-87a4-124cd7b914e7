package com.wendao101.common.core.annotation;

import com.wendao101.common.core.utils.poi.ExcelHandlerAdapter;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

/**
 * 自定义导入导出Excel数据注解
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelField {
    /**
     * 排序，该字段值越小，排序越前
     */
    int ordinal() default 0;

    /**
     * 列宽，取值范围0-255，默认16，当列宽<0或>255时，使用默认列宽
     */
    int columnWidth() default 16;

    /**
     * 字段名
     */
    String columnName();

    /**
     * 时间类型的格式化，目前支持Date, LocalDate, LocalTime, LocalDateTime四种时间类型
     */
    String format() default "yyyy-MM-dd HH:mm:ss";
}
