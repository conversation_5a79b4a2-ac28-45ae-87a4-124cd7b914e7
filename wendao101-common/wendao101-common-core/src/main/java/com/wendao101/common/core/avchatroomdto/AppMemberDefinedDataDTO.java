package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * {
 * "Key": "MemberDefined1", // 群成员维度自定义的 Key
 * "Value": "MemberData1" // 群成员维度自定义字段值
 * }
 */
@Data
public class AppMemberDefinedDataDTO {
    /**
     * 群成员维度自定义的 Key
     */
    @JsonProperty("Key")
    @JSONField(name = "Key")
    String key;
    /**
     * 群成员维度自定义字段值
     */
    @JsonProperty("Value")
    @JSONField(name = "Value")
    String value;
}
