package com.wendao101.common.core.wendao;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class HotTeacherDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long teacherId;


    /** 店铺名称同时也是登录显示名，例如“淘气包” */
    @Excel(name = "店铺名称同时也是登录显示名，例如“淘气包”")
    private String shopName;

    /** 老师真实姓名 例如“刘德华” */
    @Excel(name = "老师真实姓名 例如“刘德华”")
    private String teacherName;

    /** 教师简介 */
    @Excel(name = "教师简介")
    private String teacherDesc;

    /** 教师头像url */
    @Excel(name = "教师头像url")
    private String avatarUrl;



    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    private Integer appNameType;

    /** 销量 三平台评论量 用这个字段 */
    private Long salesVolume;
}
