package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *           "Member_Account": "bob", // 成员（必填）
 *           "AppMemberDefinedData":[ // 群成员维度自定义字段（选填）
 *              {
 *                  "Key": "MemberDefined1", // 群成员维度自定义的 Key
 *                  "Value": "MemberData1" // 群成员维度自定义字段值
 *              },
 *              {
 *                  "Key": "MemberDefined2",
 *                  "Value": "MemberData2"
 *              }
 *          ],
 *           "Role": "Admin" // 赋予该成员的身份，目前备选项只有 Admin（选填）
 *        }
 */
@Data
public class AvChatRoomMemberListDTO {
    /**
     * 成员（必填）
     */
    @JsonProperty("Member_Account")
    @JSONField(name="Member_Account")
    String memberAccount;

    /**
     * 赋予该成员的身份，目前备选项只有 Admin（选填）
     */
    @JsonProperty("Role")
    @JSONField(name="Role")
    String role;

    /**
     *  群成员维度自定义字段（选填）
     */
    @JsonProperty("AppMemberDefinedData")
    @JSONField(name="AppMemberDefinedData")
    AvChatRoomAppDefinedDataDTO[] appMemberDefinedData;
}
