package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 * "Key": "GroupTestData1", // App 自定义的字段 Key
 * "Value": "xxxxx" // 自定义字段的值
 * }
 */
@Data
public class AvChatRoomAppDefinedDataDTO {
    /**
     * App 自定义的字段 Key
     */
    @JsonProperty("Key")
    @JSONField(name = "Key")
    String key;
    /**
     * 自定义字段的值
     */
    @JsonProperty("Value")
    @JSONField(name = "Value")
    String value;
}
