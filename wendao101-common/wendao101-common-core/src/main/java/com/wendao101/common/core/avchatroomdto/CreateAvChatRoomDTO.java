package com.wendao101.common.core.avchatroomdto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *   "Owner_Account": "leckie", // 群主的 UserId（选填）
 *   "Type": "Public", // 群组类型：Private/Public/ChatRoom/AVChatRoom/Community
 *   "GroupId": "MyFirstGroup", // 用户自定义群组 ID（选填）
 *   "Name": "TestGroup" // 群名称（必填）
 *   "MemberList": [ // 初始群成员列表，最多100个（选填）
 *        {
 *           "Member_Account": "bob", // 成员（必填）
 *           "Role": "Admin", // 赋予该成员的身份，目前备选项只有 Admin（选填）
 *           "AppMemberDefinedData":[ // 群成员维度自定义字段（选填）
 *              {
 *                  "Key": "MemberDefined1", // 群成员维度自定义的 Key
 *                  "Value": "MemberData1" // 群成员维度自定义字段值
 *              },
 *              {
 *                  "Key": "MemberDefined2",
 *                  "Value": "MemberData2"
 *              }
 *          ]
 *        },
 *        {
 *           "Member_Account": "peter"
 *        }
 *    ],
 *    "AppDefinedData": [ // 群组维度的自定义字段（选填）
 *       {
 *           "Key": "GroupTestData1", // App 自定义的字段 Key
 *           "Value": "xxxxx" // 自定义字段的值
 *       },
 *       {
 *           "Key": " GroupTestData2",
 *           "Value": "abc\u0000\u0001" // 自定义字段支持二进制数据
 *       }
 *   ],
 *   //非必填项
 *   "Introduction": "This is group Introduction", // 群简介（选填）
 *   "Notification": "This is group Notification", // 群公告（选填）
 *   "FaceUrl": "http://this.is.face.url", // 群头像 URL（选填）
 *   "MaxMemberCount": 500, // 最大群成员数量（选填）
 *   "ApplyJoinOption": "FreeAccess",  // 申请加群处理方式（选填）
 *   "InviteJoinOption": "FreeAccess",//邀请加群处理方式(选填)
 *
 *   "SupportTopic": 1			//是否支持话题选项, 1代表支持,0代表不支持
 * }
 *
 *
 * 可能触发的回调
 * 创建群组之前回调
 * 创建群组之后回调
 */
@Data
public class CreateAvChatRoomDTO {
    /**
     * 群主的 UserId（选填）
     */
    @JsonProperty("Owner_Account")
    @JSONField(name="Owner_Account")
    private String ownerAccount;
    /**
     * 群组类型：Private/Public/ChatRoom/AVChatRoom/Community
     */
    @JsonProperty("Type")
    @JSONField(name="Type")
    private String type = "AVChatRoom";

    /**
     * 用户自定义群组 ID（选填）
     */
    @JsonProperty("GroupId")
    @JSONField(name="GroupId")
    private String groupId;

    /**
     * 群名称（必填）
     */
    @JsonProperty("Name")
    @JSONField(name="Name")
    private String name;

    /**
     * 初始群成员列表，最多100个（选填）
     */
    @JsonProperty("MemberList")
    @JSONField(name="MemberList")
    private AvChatRoomMemberListDTO[] memberList;

    /**
     * 群组维度的自定义字段（选填）
     */
    @JsonProperty("AppDefinedData")
    @JSONField(name="AppDefinedData")
    private AvChatRoomAppDefinedDataDTO[] appDefinedData;

    /**
     * 群简介（选填）
     */
    @JsonProperty("Introduction")
    @JSONField(name="Introduction")
    private String introduction;

    /**
     * 群公告（选填）
     */
    @JsonProperty("Notification")
    @JSONField(name="Notification")
    private String notification;

    /**
     * 群头像 URL（选填）
     */
    @JsonProperty("FaceUrl")
    @JSONField(name="FaceUrl")
    private String faceUrl;

    /**
     * 最大群成员数量（选填）
     */
    @JsonProperty("MaxMemberCount")
    @JSONField(name="MaxMemberCount")
    private Integer maxMemberCount;

    /**
     * 申请加群处理方式（选填）
     */
    @JsonProperty("ApplyJoinOption")
    @JSONField(name="ApplyJoinOption")
    private String applyJoinOption;

    /**
     * 邀请加群处理方式(选填)
     */
    @JsonProperty("InviteJoinOption")
    @JSONField(name="InviteJoinOption")
    private String inviteJoinOption;

    /**
     * 是否支持话题选项, 1代表支持,0代表不支持
     */
    @JsonProperty("SupportTopic")
    @JSONField(name="SupportTopic")
    private Integer supportTopic = 0;

}
