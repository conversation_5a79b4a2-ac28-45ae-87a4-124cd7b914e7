package com.wendao101.common.core.aliyunlive.miclive;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wendao101.common.core.aliyunlive.miclive.child.LinkInfo;
import com.wendao101.common.core.aliyunlive.miclive.child.PullLiveInfo;
import com.wendao101.common.core.aliyunlive.miclive.child.PushUrlInfo;
import com.wendao101.common.core.aliyunlive.miclive.child.VodInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MicLiveDTO {
    @JsonProperty("push_url_info")
    private PushUrlInfo pushLiveInfo;
    @JsonProperty("vod_info")
    private VodInfo vodInfo;
    @JsonProperty("chat_id")
    private String chatId;
    @JsonProperty("link_info")
    private LinkInfo linkInfo;
    @JsonProperty("pull_url_info")
    private PullLiveInfo pullLiveInfo;
    @JsonProperty("anchor_id")
    private String anchorId;
    @JsonProperty("meeting_id")
    private String meetingId;
    @JsonProperty("title")
    private String title;
    @JsonProperty("mode")
    private Integer mode;
    /**
     * 扩展信息
     */
    @JsonProperty("extends")
    private String extendsInfo;
    /**
     * 连麦成员信息（json序列化）
     */
    @JsonProperty("meeting_info")
    private String meetingInfo;

    /**
     * 主播Nick
     */
    @JsonProperty("anchor_nick")
    private String anchorNick;

    /**
     * 直播状态，0-准备中，1-已开始，2-已结束
     */
    @JsonProperty("status")
    private Integer status;
}
