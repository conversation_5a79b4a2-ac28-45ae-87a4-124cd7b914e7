package com.wendao101.common.core.douyin.clock.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 打卡任务对象 clock_in_quest
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockInQuestDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 课程名称
     */
    @Excel(name = "课程名称")
    private String courseName;

    /**
     * 打卡标题
     */
    @Excel(name = "打卡标题")
    private String clockTitle;

    /**
     * 参与人数
     */
    @Excel(name = "参与人数")
    private Long participateNumber;

    /**
     * 虚拟人数
     */
    @Excel(name = "虚拟人数")
    private Long virtualNumber;

    /**
     * 虚拟头像列表
     */
    @Excel(name = "虚拟头像列表")
    private String virtualImg;

    /**
     * 任务类型 0打卡 1作业
     */
    @Excel(name = "任务类型 0打卡 1作业")
    private Integer questType;

    /**
     * 任务状态 0进行中 1已结束 2禁用
     */
    @Excel(name = "任务状态 0进行中 1已结束 2禁用")
    private Integer questStatus;

    /**
     * 任务周期    天
     */
    @Excel(name = "任务周期    天")
    private Integer questPeriod;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 参与人数
     */
    @Excel(name = "参与人数")
    private Long number;
}
