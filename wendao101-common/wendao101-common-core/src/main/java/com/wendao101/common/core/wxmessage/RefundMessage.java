package com.wendao101.common.core.wxmessage;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 参数长度限制
 * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html#%E7%B1%BB%E7%9B%AE%E6%A8%A1%E6%9D%BF%E6%B6%88%E6%81%AF
 */
@Data
public class RefundMessage {
    //    订单编号
//    {{character_string8.DATA}}
//    商品名称
//    {{thing9.DATA}}
//    退款原因
//    {{thing3.DATA}}
//    退款金额
//    {{amount7.DATA}}
//    退款时间
//    {{time4.DATA}}
    private Long teacherId;
    private String orderId;
    private String courseTitle;
    private String refundReason;
    private BigDecimal refundPrice;
    private Date refundTime;

    private String ktTemplateId = "W5xplpkVA18EvEOKsKm5grsOTOgKFAf_-RyVBPP1m34";
    private String hkTemplateId = "yk6Sjb_4n8XpSC_hYkQfKOuJLpXKN1RN0dZaB7E9_WE";


    public String getCourseTitle() {
        if (courseTitle == null) {
            return null;
        }
        //20个以内字符	可汉字、数字、字母或符号组合
        return courseTitle.length() > 20 ? courseTitle.substring(0, 20) : courseTitle;
    }

    public String getRefundReason() {
        if (refundReason == null) {
            return null;
        }
        //20个以内字符	可汉字、数字、字母或符号组合
        return refundReason.length() > 20 ? refundReason.substring(0, 20) : refundReason;
    }
}
