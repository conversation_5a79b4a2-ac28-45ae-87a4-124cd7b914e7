package com.wendao101.common.core.douyin.clock.request;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 打卡回复对象 clock_reply
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class AddClockReplyRequest{

    /**
     *打卡回复主键id
     */
    @Excel(name = "打卡回复主键id")
    private Long id;

    /**
     * 平台open_id
     */
    @Excel(name = "平台open_id")
    private String openId;


    /**
     * 打卡记录表id
     */
    @Excel(name = "打卡记录表id")
    private Long clockRecordId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    /**
     * 回复人id
     */
    @Excel(name = "回复人id")
    private Long replyId;

    /**
     * 回复人名称
     */
    @Excel(name = "回复人名称")
    private String replyName;

    /**
     * 回复人头像
     */
    @Excel(name = "回复人头像")
    private String replyImg;


    private Integer appNameType=1;
}
