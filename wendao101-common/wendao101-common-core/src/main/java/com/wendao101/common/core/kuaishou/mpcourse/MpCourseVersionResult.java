package com.wendao101.common.core.kuaishou.mpcourse;

import com.wendao101.common.core.kuaishou.mpcourse.child.MpCourseVersionResultData;
import lombok.Data;

import java.util.List;

/**
 * 上线状态说明
 * onlineStatus状态值	含义
 * 1	已上线
 * 2	未上线
 * {
 *     "result": 1,
 *     "error_msg": "success",
 *     "data": [
 *         {
 *             "version": 2,
 *             "auditStatus": 2,
 *             "auditStatusDesc": "审核通过",
 *             "onlineStatus": 2,
 *             "onlineStatusDesc": "未上线"
 *         },
 *         {
 *             "version": 1,
 *             "auditStatus": 3,
 *             "auditStatusDesc": "审核拒绝",
 *             "onlineStatus": 2,
 *             "onlineStatusDesc": "未上线"
 *         }
 *     ]
 * }
 */
@Data
public class MpCourseVersionResult {
    private Integer result;
    private String error_msg;
    private List<MpCourseVersionResultData> data;
}
