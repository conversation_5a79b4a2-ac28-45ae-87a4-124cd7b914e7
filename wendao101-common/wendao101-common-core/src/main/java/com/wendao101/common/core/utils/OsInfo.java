package com.wendao101.common.core.utils;

import com.wendao101.common.core.enums.EPlatform;

/**
 * 操作系统类：
 * 获取System.getProperty("os.name")对应的操作系统
 * <AUTHOR>
 */
public class OsInfo {

    private static String OS = System.getProperty("os.name").toLowerCase();

    private EPlatform platform;

    public static boolean isLinux(){
        return OS.contains("linux");
    }

    public static boolean isMac(){
        return OS.contains("mac") &&OS.indexOf("os")>0;
    }

    public static boolean isWindows(){
        return OS.contains("windows");
    }

}
