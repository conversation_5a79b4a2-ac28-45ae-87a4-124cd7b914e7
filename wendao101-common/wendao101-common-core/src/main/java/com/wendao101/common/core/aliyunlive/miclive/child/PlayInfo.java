package com.wendao101.common.core.aliyunlive.miclive.child;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlayInfo {
    @JsonProperty("bit_depth")
    private Integer bitDepth;
    @JsonProperty("bit_rate")
    private String bitRate;
    @JsonProperty("creation_time")
    private String creationTime;
    private String definition;
    private String duration;
    private Long encrypt;
    @JsonProperty("encrypt_type")
    private String encryptType;
    private String format;
    private String fps;
    @JsonProperty("hdr_type")
    private String hDRType;
    private long height;
    private long width;
    @JsonProperty("play_url")
    private String playUrl;
    private Long size;
    private String status;
    @JsonProperty("stream_type")
    private String streamType;
    @JsonProperty("watermark_id")
    private String watermarkId;
}
