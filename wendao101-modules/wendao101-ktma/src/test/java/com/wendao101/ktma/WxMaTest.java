package com.wendao101.ktma;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.wendao101.common.redis.service.RedisService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class WxMaTest {
    @Autowired
    private WxMaService wxService;
    @Autowired
    private RedisService redisService;
    @Test
    public void test1() throws WxErrorException {
//        String accessToken = wxService.getAccessToken();
//        System.out.println(accessToken);
    }

//    @Test
//    public void test2() throws WxErrorException {
//        for(int i=0;i<10;i++){
//            long incr = redisService.incr("202344127", 1586954);
//            System.out.println(incr);
//        }
//    }

    @Test
    public void test3() throws WxErrorException {
        String appid = wxService.getWxMaConfig().getAppid();
        String secret = wxService.getWxMaConfig().getSecret();
        String aesKey = wxService.getWxMaConfig().getAesKey();
        String token = wxService.getWxMaConfig().getToken();
        String msgDataFormat = wxService.getWxMaConfig().getMsgDataFormat();

        String accessToken = wxService.getAccessToken();
        System.out.println(appid);
        System.out.println(secret);
        System.out.println(aesKey);
        System.out.println(token);
        System.out.println(msgDataFormat);
        System.out.println(accessToken);
        //String accessToken = wxService.getAccessToken();
        //System.out.println(accessToken);
    }


}
