package com.wendao101.ktma.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.ktma.api.dto.SendMailDTO;
import com.wendao101.ktma.service.MailSendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/emailSend")
public class EmailSendController extends BaseController {
    @Autowired
    private MailSendService mailSendService;

    @PostMapping("/sendMail")
    public AjaxResult sendMail(@RequestBody SendMailDTO request) {
        mailSendService.sendMail(request.getEmailAddress(), request.getSubject(), request.getContent(), request.getFileUrl(), request.getFileName());
        return AjaxResult.success();
    }
}
