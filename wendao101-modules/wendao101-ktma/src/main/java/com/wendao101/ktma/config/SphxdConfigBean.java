package com.wendao101.ktma.config;

import com.wendao101.common.core.utils.http.HttpUtils;
import lombok.Getter;
import lombok.Setter;
import me.chanjar.weixin.channel.config.impl.WxChannelDefaultConfigImpl;

@Getter
@Setter
public class SphxdConfigBean extends WxChannelDefaultConfigImpl {
    private static final String key = "87er8wefsd5f4e8wr8ew78rew";
    @Override
    public String getAccessToken() {
        //获取accessToken实现
        String url  = "https://goodminiapp.wendao101.com/kt_access_token_controller/getKtSphToken?requestKey="+key;
        return HttpUtils.sendGet(url);
    }

    @Override
    public boolean isAccessTokenExpired() {
        return false;
    }
}
