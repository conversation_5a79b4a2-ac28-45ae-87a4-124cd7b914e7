package com.wendao101.ktma.service.impl;

import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.ktma.service.MailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

@Slf4j
@Service
public class MailSendServiceImpl implements MailSendService {
    @Async
    @Override
    public void sendMail(String emailAddress, String subject, String content, String fileUrl,String originalFileName) {
        if("<EMAIL>".equals(emailAddress)){
            return;
        }
        // 发件人邮箱和密码
        final String username = "<EMAIL>";
        final String password = "A2qBxw2dkGzdoDni";

        // 设置邮件属性
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", "smtp.exmail.qq.com"); // 使用腾讯企业邮箱SMTP服务器
        props.put("mail.smtp.port", "465");
        props.put("mail.smtp.ssl.enable", "true");

        // 创建会话
        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        try {
            // 创建邮件消息
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(emailAddress));
            message.setSubject(subject);

            // 创建邮件正文
            BodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setText(content);

            // 创建多重消息
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(messageBodyPart);
            // 添加附件
            messageBodyPart = new MimeBodyPart();
            URL url = new URL(fileUrl);
            URLConnection connection = url.openConnection();
            // 获取文件名
            String fileName = StringUtils.isNotBlank(originalFileName)?originalFileName:fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
            // 获取MIME类型
            String mimeType = connection.getContentType();
            if (mimeType == null) {
                // 如果无法获取MIME类型，则根据文件扩展名猜测
                mimeType = URLConnection.guessContentTypeFromName(fileUrl.substring(fileUrl.lastIndexOf('/') + 1));
            }
            if (mimeType == null) {
                // 如果仍然无法确定，使用通用二进制类型
                mimeType = "application/octet-stream";
            }
            System.out.println("mimeType:" + mimeType);
            InputStream inputStream = connection.getInputStream();
            messageBodyPart.setDataHandler(new DataHandler(new ByteArrayDataSource(inputStream, mimeType)));
            
            // 获取原始文件的扩展名
            String originalFileExtension = fileUrl.substring(fileUrl.lastIndexOf('.'));
            
            // 定义合法的文件扩展名列表
            Set<String> validExtensions = new HashSet<>(Arrays.asList(
                ".doc", ".docx", ".pdf", ".txt", ".xls", ".xlsx", 
                ".ppt", ".pptx", ".zip", ".rar", ".7z", 
                ".jpg", ".jpeg", ".png", ".gif"
            ));
            
            // 获取fileName的扩展名（如果有的话）
            String currentExtension = fileName.contains(".") ? 
                fileName.substring(fileName.lastIndexOf('.')).toLowerCase() : "";
                
            // 如果fileName没有扩展名或扩展名不合法，直接加上原始文件的扩展名
            if (!validExtensions.contains(currentExtension.toLowerCase())) {
                fileName = fileName + originalFileExtension;
            }
            
            messageBodyPart.setFileName(fileName);
            multipart.addBodyPart(messageBodyPart);
            // 将多重消息设置为邮件内容
            message.setContent(multipart);
            // 发送邮件
            Transport.send(message);
            System.out.println("邮件发送成功！");
        } catch (MessagingException | IOException e) {
            e.printStackTrace();
        }
    }
}
