package com.wendao101.ktma.config;

import me.chanjar.weixin.channel.api.WxChannelService;
import me.chanjar.weixin.channel.api.impl.WxChannelServiceImpl;
import me.chanjar.weixin.channel.config.WxChannelConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SphxdConfig {
    @Bean
    public WxChannelConfig wxChannelConfig() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wx72e0b2963317efe0");
        config.setSecret("c43347ebf4c42d9b3f317b0f21d95805");
        config.setMsgDataFormat("JSON");
        return config;
    }

    @Bean
    public WxChannelService wxChannelService(WxChannelConfig wxChannelConfig) {
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(wxChannelConfig);
        return service;
    }
}
