package com.wendao101.ktma.controller;

import cn.binarywang.wx.miniapp.api.WxMaLinkService;
import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import com.wendao101.common.core.constant.CacheConstants;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.ktma.api.dto.*;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/login")
public class LoginController extends BaseController {
    @Autowired
    private WxMaService wxService;

    @Autowired
    private RedisService redisService;

    @PostMapping("/getWxOpenId")
    public GetKtWxOpenIdResult getWxOpenId(@RequestBody KtWxJsCode2SessionDTO ksJsCode2SessionDTO){
        try {
            WxMaJscode2SessionResult result = wxService.getUserService().getSessionInfo(ksJsCode2SessionDTO.getJsCode());
            return GetKtWxOpenIdResult.builder().msg("成功").code(200).appId(wxService.getWxMaConfig().getAppid()).result(result).build();
        } catch (WxErrorException e) {
            return GetKtWxOpenIdResult.builder().msg(e.getError().getErrorMsg()).code(500).result(null).build();
        }
    }

    @PostMapping("/getWxUserPhone")
    public GetKtWxUserPhoneResult getWxUserPhone(@RequestBody GetKtWxUserPhoneDTO getWxUserPhoneDTO){
        try {
            WxMaPhoneNumberInfo phoneNoInfo = wxService.getUserService().getPhoneNoInfo(getWxUserPhoneDTO.getCode());
            return GetKtWxUserPhoneResult.builder().msg("成功").code(200).result(phoneNoInfo).build();
        } catch (WxErrorException e) {
            return GetKtWxUserPhoneResult.builder().msg(e.getError().getErrorMsg()).code(500).result(null).build();
        }
    }

    @PostMapping("/getKtWxQrcode")
    public GetKtWxQrcodeResult getKtWxQrcode(@RequestBody GetKtWxQrcodeDTO getKtWxQrcodeDTO){
        /**
         * 微信
         */
        String platform = "1";
        /**
         * 课堂
         */
        int appNameType = 2;
        String s = RandomStringUtils.randomAlphabetic(16);
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            byte[] qrcode = qrcodeService.createQrcodeBytes("pages_details/details/details?id=" + getKtWxQrcodeDTO.getCourseId() + "&platform=1&appNameType=2&tempSeeSecret=" + s, 430);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            redisService.setCacheObject(CacheConstants.QRCODE_SEE_VIDEO_KEY + getKtWxQrcodeDTO.getCourseId()+platform+appNameType, s, 7200L, TimeUnit.SECONDS);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/getKtWxMiniAppQrcode")
    public GetKtWxQrcodeResult getKtWxMiniAppQrcode(@RequestBody GetKtWxQrcodeDTO getKtWxQrcodeDTO){
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            String scene = getKtWxQrcodeDTO.getCourseId() + "_1_2";//以下划线分隔，第一个为课程id，第二个为platform，第三个为appNameType
            WxMaCodeLineColor wxMaCodeLineColor = new WxMaCodeLineColor();
            byte[] qrcode = qrcodeService.createWxaCodeUnlimitBytes(scene,"pages_details/details/details", true,"release",430,false,wxMaCodeLineColor,false);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/getKtWxQrcodeForSphPromoter")
    public GetKtWxQrcodeResult getKtWxQrcodeForSphPromoter(@RequestBody GetKtWxQrcodeSphPromoterDTO getKtWxQrcodeSphPromoterDTO) {
        /**
         * 课堂
         */
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            byte[] qrcode = qrcodeService.createQrcodeBytes("pages_mine/add_video_promoter/add_video_promoter?phoneNumber="+getKtWxQrcodeSphPromoterDTO.getPhoneNumber()+"&teacherId=" +
                    getKtWxQrcodeSphPromoterDTO.getTeacherId() + "&requestId=" +
                    getKtWxQrcodeSphPromoterDTO.getRequestId(), 430);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/getKtWxQrcodeForDdPromoter")
    public GetKtWxQrcodeResult getKtWxQrcodeForDdPromoter(@RequestBody GetKtWxQrcodeSphPromoterDTO getKtWxQrcodeSphPromoterDTO) {

        //16位随机字符串
//        QrCodeDTO qrCodeDTO = new QrCodeDTO();
//        qrCodeDTO.setAppname("all");
//        qrCodeDTO.set_circle_code(false);
//        qrCodeDTO.setSet_icon(true);
//        qrCodeDTO.setPath("pages_mine%2Fadd_doudian_promoter%2Fadd_doudian_promoter%3FphoneNumber%3D" + promoterCourseVO.getPromoterPhone() + "%26teacherId%3D"+teacherId+"%26requestId%3D"+requestId);
//        qrCodeDTO.setWidth(430);
//        qrCodeDTO.setAccess_token(accessToken);
//        byte[] qrcode = douyinQrcodeService.qrcode(qrCodeDTO);
//        // 使用 Base64 编码将 byte[] 数组转换为字符串
//        return Base64.getEncoder().encodeToString(qrcode);
        /**
         * 课堂
         */
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            byte[] qrcode = qrcodeService.createQrcodeBytes("pages_mine/add_doudian_promoter/add_doudian_promoter?phoneNumber="+getKtWxQrcodeSphPromoterDTO.getPhoneNumber()+"&teacherId=" +
                    getKtWxQrcodeSphPromoterDTO.getTeacherId() + "&requestId=" +
                    getKtWxQrcodeSphPromoterDTO.getRequestId(), 430);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/getKtWxQrcodeLogin")
    public GetKtWxQrcodeResult getKtWxQrcodeLogin(@RequestBody GetKtWxQrcodeLoginDTO getKtWxQrcodeLoginDTO){
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            byte[] qrcode = qrcodeService.createQrcodeBytes("pages/pcWatchCourse/pcWatchCourse?uuid=" + getKtWxQrcodeLoginDTO.getUuid() + "&platform=1&appNameType=2", 430);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/getKtWxQrcodeForPromoter")
    public GetKtWxQrcodeResult getKtWxQrcodeForPromoter(@RequestBody GetKtWxQrcodeForPromoterDTO getKtWxQrcodeForPromoterDTO){
        GetKtWxQrcodeResult getKtWxQrcodeResult = new GetKtWxQrcodeResult();
        getKtWxQrcodeResult.setCode(200);
        getKtWxQrcodeResult.setMsg("成功");
        WxMaQrcodeService qrcodeService = wxService.getQrcodeService();
        try {
            byte[] qrcode = qrcodeService.createQrcodeBytes("pages_mine/promoter/promoter?id=" + getKtWxQrcodeForPromoterDTO.getId() + "&platform=1&appNameType=2", 430);
            String base64String = Base64.getEncoder().encodeToString(qrcode);
            getKtWxQrcodeResult.setData(base64String);
        } catch (WxErrorException e) {
            getKtWxQrcodeResult.setCode(500);
            getKtWxQrcodeResult.setMsg(e.getError().getErrorMsg());
        }
        return getKtWxQrcodeResult;
    }

    @PostMapping("/generateUrlLink")
    public AjaxResult generateUrlLink(@RequestBody GenerateUrlLinkRequest request) {
        WxMaLinkService linkService = wxService.getLinkService();
        try {
            String generate = linkService.generateUrlLink(request);
            return AjaxResult.success("success", generate);
        } catch (WxErrorException e) {
            return AjaxResult.error(e.getError().getErrorMsg());
        }
    }
}
