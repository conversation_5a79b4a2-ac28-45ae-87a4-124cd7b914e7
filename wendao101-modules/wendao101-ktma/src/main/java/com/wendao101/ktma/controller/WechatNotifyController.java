package com.wendao101.ktma.controller;


import com.wendao101.ktma.api.feign.HkSphxdRedirectService;
import com.wendao101.ktma.service.ChannelMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信回调消息 控制器
 *
 * <AUTHOR> href="https://github.com/lixize">Zeyes</a>
 */
@Slf4j
@RestController
@RequestMapping("/wxShop/notify/{appid}")
public class WechatNotifyController {
    @Autowired
    private ChannelMessageService channelMessageService;

    @Autowired
    private HkSphxdRedirectService hkSphxdRedirectService;

    /**
     * 接收微信服务器的认证消息
     *
     * @param appid     视频号appId(注意这是路径的参数)
     * @param signature 微信加密签名
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @param echostr   随机字符串
     * @return echostr
     */
    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable String appid,
                          @RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("\n接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (channelMessageService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        return "非法请求";
    }

    /**
     * 接收微信服务器的消息
     *
     * @param appid        视频号appId(注意这是路径的参数)
     * @param requestBody  消息体
     * @param msgSignature 签名串
     * @param encryptType  加密方式
     * @param signature    微信加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @return String
     */
    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable String appid,
                       @RequestBody String requestBody,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature,
                       @RequestParam(name = "encrypt_type", required = false) String encryptType,
                       @RequestParam(name = "signature", required = false) String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce) {
        log.info("\n接收微信请求：[appid=[{}], msg_signature=[{}], encrypt_type=[{}], signature=[{}]," +
                        " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                appid, msgSignature, encryptType, signature, timestamp, nonce, requestBody);
        
        //转发到问到好课
        try {
            // 统一调用，根据appid动态路由
            hkSphxdRedirectService.postByAppid(appid, requestBody, msgSignature, encryptType, signature, timestamp, nonce);
        } catch (Exception e) {
            log.error("转发微信消息失败: {}", e.getMessage(), e);
        }
        
        return "success";
    }
}
