# Tomcat
server:
  port: 8080

# Spring
spring: 
  application:
    # 应用名称
    name: wendao101-gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      username: wendaonacos
      password: gI6d8Y6ljLSc
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.address@
      config:
        # 配置中心地址
        server-addr: @nacos.server.address@
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: @nacos.server.address@
            dataId: sentinel-wendao101-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
